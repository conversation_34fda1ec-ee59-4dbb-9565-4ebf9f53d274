"use server";

import { createAction } from "@/lib/actions/core";
import { ApiResponse } from "@/lib/actions/types";
import { createNotification } from "@/lib/actions/notifications";
import { z } from "zod";

// Validasyon şemaları
const membershipRequestSchema = z.object({
  gymId: z.string().uuid("Geçerli bir salon ID'si gereklidir"),
  userId: z.string().uuid("Geçerli bir kullanıcı ID'si gereklidir"),
});

/**
 * Salona üyelik isteği gönderir (Bildirim tabanlı yaklaşım)
 * Artık memberships tablosuna kayıt eklemiyor, sadece bildirim gönderiyor
 */
export async function requestGymMembership(
  gymId: string,
  userId: string
): Promise<ApiResponse> {
  if (!gymId || !userId) {
    return { success: false, error: "Gerekli bilgiler eksik." };
  }

  return await createAction(
    async (_, supabase, authUserId) => {
      // Güvenlik kontrolü: Kullanıcı sadece kendi adına istek gönderebilir
      if (authUserId !== userId) {
        throw new Error(
          "Sadece kendi adınıza üyelik isteği gönderebilirsiniz."
        );
      }

      // Mevcut üyelik kontrolü - zaten üye mi?
      const { data: existingMembership } = await supabase
        .from("memberships")
        .select("*")
        .eq("user_id", userId)
        .eq("gym_id", gymId)
        .maybeSingle();

      if (existingMembership) {
        throw new Error("Bu salona zaten üyeliğiniz bulunmaktadır.");
      }

      // Salon bilgilerini al
      const { data: gym, error: gymError } = await supabase
        .from("gyms")
        .select("*")
        .eq("id", gymId)
        .single();

      if (gymError || !gym) {
        throw new Error("Salon bilgileri alınamadı.");
      }

      // Kullanıcı bilgilerini al
      const { data: user, error: userError } = await supabase
        .from("users")
        .select("name, surname, email")
        .eq("id", userId)
        .single();

      if (userError || !user) {
        throw new Error("Kullanıcı bilgileri alınamadı.");
      }

      // Daha önce aynı salon için bekleyen istek var mı kontrol et
      const { data: existingRequest } = await supabase
        .from("notifications")
        .select("*")
        .eq("user_id", gym.manager_user_id)
        .eq("type", "membership_request")
        .eq("related_entity_type", "gym_membership_request")
        .like("metadata", `%"requesterId":"${userId}"%`)
        .like("metadata", `%"gymId":"${gymId}"%`)
        .eq("is_read", false)
        .maybeSingle();

      if (existingRequest) {
        throw new Error(
          "Bu salon için zaten bekleyen bir üyelik isteğiniz bulunmaktadır."
        );
      }

      // Salon yöneticisine bildirim oluştur (metadata ile istek bilgilerini sakla)
      await createNotification({
        userId: gym.manager_user_id,
        title: "Yeni Üyelik İsteği",
        message: `${user.name} ${user.surname} (${user.email}) ${gym.name} salonunuza üyelik isteği gönderdi.`,
        type: "membership_request",
        relatedEntityType: "gym_membership_request",
        relatedEntityId: `${userId}_${gymId}`,
        metadata: {
          requesterId: userId,
          requesterName: `${user.name} ${user.surname}`,
          requesterEmail: user.email,
          gymId: gymId,
          gymName: gym.name,
          requestDate: new Date().toISOString(),
        },
      });

      // Kullanıcıya bildirim oluştur
      await createNotification({
        userId,
        title: "Üyelik İsteği Gönderildi",
        message: `${gym.name} salonuna üyelik isteğiniz gönderildi. Salon yöneticisi isteğinizi değerlendirdikten sonra bilgilendirileceksiniz.`,
        type: "membership_request_sent",
        relatedEntityType: "gym_membership_request",
        relatedEntityId: `${userId}_${gymId}`,
      });

      return {
        message: "Üyelik isteği başarıyla gönderildi.",
        gymName: gym.name,
      };
    },
    {
      requireAuth: true,
      revalidatePaths: [`/gyms/${gymId}`, "/dashboard/member"],
    }
  );
}

/**
 * Bildirim tabanlı üyelik isteğini kabul eder
 * Notification metadata'sından bilgileri alarak üyelik oluşturur
 */
export async function acceptMembershipRequestFromNotification(
  notificationId: string
): Promise<ApiResponse> {
  if (!notificationId) {
    return { success: false, error: "Bildirim ID'si gereklidir." };
  }

  return await createAction(
    async (_, supabase, authUserId) => {
      // Bildirimi al ve metadata'yı parse et
      const { data: notification, error: notificationError } = await supabase
        .from("notifications")
        .select("*")
        .eq("id", notificationId)
        .eq("user_id", authUserId)
        .eq("type", "membership_request")
        .single();

      if (notificationError || !notification) {
        throw new Error("Bildirim bulunamadı veya erişim yetkiniz yok.");
      }

      if (notification.is_read) {
        throw new Error("Bu istek zaten işlenmiş.");
      }

      // Metadata'yı parse et
      let metadata;
      try {
        metadata = JSON.parse(notification.metadata || "{}");
      } catch (error) {
        throw new Error("Bildirim verisi bozuk.");
      }

      const { requesterId, gymId, requesterName } = metadata;

      if (!requesterId || !gymId) {
        throw new Error("Gerekli bilgiler eksik.");
      }

      // Salon yöneticisi yetkisi kontrolü
      const { data: gym, error: gymError } = await supabase
        .from("gyms")
        .select("manager_user_id, name")
        .eq("id", gymId)
        .single();

      if (gymError || !gym || gym.manager_user_id !== authUserId) {
        throw new Error("Bu salon için yetkiniz bulunmuyor.");
      }

      // Zaten üye mi kontrol et
      const { data: existingMembership } = await supabase
        .from("memberships")
        .select("*")
        .eq("user_id", requesterId)
        .eq("gym_id", gymId)
        .maybeSingle();

      if (existingMembership) {
        throw new Error("Bu kullanıcı zaten salona üye.");
      }

      // Üyelik oluştur
      const { data: membership, error: membershipError } = await supabase
        .from("memberships")
        .insert({
          user_id: requesterId,
          gym_id: gymId,
          status: "approved_passive",
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (membershipError) {
        throw new Error("Üyelik oluşturulurken hata oluştu.");
      }

      // Bildirimi okundu olarak işaretle
      await supabase
        .from("notifications")
        .update({ is_read: true })
        .eq("id", notificationId);

      // Kullanıcıya onay bildirimi gönder
      await createNotification({
        userId: requesterId,
        title: "Üyelik İsteği Kabul Edildi",
        message: `${gym.name} salonuna üyelik isteğiniz kabul edildi! Artık paket satın alabilirsiniz.`,
        type: "membership_approved",
        relatedEntityType: "memberships",
        relatedEntityId: membership.id,
      });

      return {
        membership,
        message: `${requesterName} başarıyla salona eklendi.`,
      };
    },
    {
      requireAuth: true,
      revalidatePaths: [
        "/dashboard/manager/notifications",
        "/dashboard/manager/members",
      ],
    }
  );
}

/**
 * Bildirim tabanlı üyelik isteğini reddeder
 */
export async function rejectMembershipRequestFromNotification(
  notificationId: string,
  reason?: string
): Promise<ApiResponse> {
  if (!notificationId) {
    return { success: false, error: "Bildirim ID'si gereklidir." };
  }

  return await createAction(
    async (_, supabase, authUserId) => {
      // Bildirimi al ve metadata'yı parse et
      const { data: notification, error: notificationError } = await supabase
        .from("notifications")
        .select("*")
        .eq("id", notificationId)
        .eq("user_id", authUserId)
        .eq("type", "membership_request")
        .single();

      if (notificationError || !notification) {
        throw new Error("Bildirim bulunamadı veya erişim yetkiniz yok.");
      }

      if (notification.is_read) {
        throw new Error("Bu istek zaten işlenmiş.");
      }

      // Metadata'yı parse et
      let metadata;
      try {
        metadata = JSON.parse(notification.metadata || "{}");
      } catch (error) {
        throw new Error("Bildirim verisi bozuk.");
      }

      const { requesterId, gymId, requesterName, gymName } = metadata;

      if (!requesterId || !gymId) {
        throw new Error("Gerekli bilgiler eksik.");
      }

      // Salon yöneticisi yetkisi kontrolü
      const { data: gym, error: gymError } = await supabase
        .from("gyms")
        .select("manager_user_id")
        .eq("id", gymId)
        .single();

      if (gymError || !gym || gym.manager_user_id !== authUserId) {
        throw new Error("Bu salon için yetkiniz bulunmuyor.");
      }

      // Bildirimi okundu olarak işaretle
      await supabase
        .from("notifications")
        .update({ is_read: true })
        .eq("id", notificationId);

      // Kullanıcıya red bildirimi gönder
      const rejectionMessage = reason
        ? `${gymName} salonuna üyelik isteğiniz reddedildi. Sebep: ${reason}`
        : `${gymName} salonuna üyelik isteğiniz reddedildi.`;

      await createNotification({
        userId: requesterId,
        title: "Üyelik İsteği Reddedildi",
        message: rejectionMessage,
        type: "membership_rejected",
        relatedEntityType: "gym_membership_request",
        relatedEntityId: `${requesterId}_${gymId}`,
      });

      return {
        message: `${requesterName} kullanıcısının üyelik isteği reddedildi.`,
      };
    },
    {
      requireAuth: true,
      revalidatePaths: ["/dashboard/manager/notifications"],
    }
  );
}

/**
 * Üyelik isteğini onaylar (ESKİ YÖNTEM - DEPRECATED)
 */
export async function approveMembershipRequest(
  membershipId: string
): Promise<ApiResponse> {
  if (!membershipId) {
    return { success: false, error: "Üyelik ID'si gereklidir." };
  }

  return await createAction(
    async (_, supabase) => {
      // Üyelik bilgilerini al
      const { data: membership, error: membershipError } = await supabase
        .from("memberships")
        .select("*")
        .eq("id", membershipId)
        .single();

      if (membershipError || !membership) {
        throw new Error("Üyelik isteği bulunamadı.");
      }

      // Üyelik durumunu güncelle
      const { error: updateError } = await supabase
        .from("memberships")
        .update({
          status: "approved_passive",
          approval_date: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .eq("id", membershipId);

      if (updateError) {
        throw new Error("Üyelik isteği onaylanırken bir hata oluştu.");
      }

      // Salon bilgilerini al
      const { data: gym, error: gymError } = await supabase
        .from("gyms")
        .select("*")
        .eq("id", membership.gym_id)
        .single();

      if (gymError || !gym) {
        throw new Error("Salon bilgileri alınamadı.");
      }

      // Kullanıcıya bildirim gönder
      await createNotification({
        userId: membership.user_id,
        title: "Üyelik İsteği Onaylandı",
        message: `${gym.name} salonuna üyelik isteğiniz onaylandı. Artık paket satın alabilirsiniz.`,
        type: "membership_approved",
        relatedEntityType: "memberships",
        relatedEntityId: membershipId,
      });

      return { membership };
    },
    { revalidatePaths: ["/dashboard/manager/members"] }
  );
}

/**
 * Üyelik isteğini reddeder
 */
export async function rejectMembershipRequest(
  membershipId: string
): Promise<ApiResponse> {
  if (!membershipId) {
    return { success: false, error: "Üyelik ID'si gereklidir." };
  }

  return await createAction(
    async (_, supabase) => {
      // Üyelik bilgilerini al
      const { data: membership, error: membershipError } = await supabase
        .from("memberships")
        .select("*")
        .eq("id", membershipId)
        .single();

      if (membershipError || !membership) {
        throw new Error("Üyelik isteği bulunamadı.");
      }

      // Üyelik durumunu güncelle
      const { error: updateError } = await supabase
        .from("memberships")
        .update({
          status: "rejected",
          updated_at: new Date().toISOString(),
        })
        .eq("id", membershipId);

      if (updateError) {
        throw new Error("Üyelik isteği reddedilirken bir hata oluştu.");
      }

      // Salon bilgilerini al
      const { data: gym, error: gymError } = await supabase
        .from("gyms")
        .select("*")
        .eq("id", membership.gym_id)
        .single();

      if (gymError || !gym) {
        throw new Error("Salon bilgileri alınamadı.");
      }

      // Kullanıcıya bildirim gönder
      await createNotification({
        userId: membership.user_id,
        title: "Üyelik İsteği Reddedildi",
        message: `${gym.name} salonuna üyelik isteğiniz reddedildi.`,
        type: "membership_rejected",
        relatedEntityType: "memberships",
        relatedEntityId: membershipId,
      });

      return { membership };
    },
    { revalidatePaths: ["/dashboard/manager/members"] }
  );
}

/**
 * Kullanıcının tüm salon üyeliklerini getirir
 */
export async function getMembershipsByUserId(
  userId: string
): Promise<ApiResponse> {
  if (!userId) {
    return { success: false, error: "Kullanıcı ID'si gereklidir." };
  }

  return await createAction(async (_, supabase) => {
    const { data, error } = await supabase
      .from("memberships")
      .select(
        `
        *,
        gym:gyms(*)
      `
      )
      .eq("user_id", userId);

    if (error) {
      throw new Error(`Üyelikler getirilirken hata: ${error.message}`);
    }

    return data || [];
  });
}

/**
 * Belirli bir salondaki tüm üyelikleri getirir
 */
export async function getMembershipsByGymId(
  gymId: string
): Promise<ApiResponse> {
  if (!gymId) {
    return { success: false, error: "Salon ID'si gereklidir." };
  }

  return await createAction(
    async (_, supabase, authUserId) => {
      // Salon yöneticisi yetkisi kontrolü
      const { data: gym } = await supabase
        .from("gyms")
        .select("manager_user_id")
        .eq("id", gymId)
        .single();

      if (!gym || gym.manager_user_id !== authUserId) {
        throw new Error(
          "Bu salonun üyeliklerini görüntülemek için yetkiniz bulunmuyor."
        );
      }

      const { data, error } = await supabase
        .from("memberships")
        .select(
          `
        *,
        user:users(*)
      `
        )
        .eq("gym_id", gymId);

      if (error) {
        throw new Error(`Salon üyelikleri getirilirken hata: ${error.message}`);
      }

      return data || [];
    },
    { requireAuth: true, revalidatePaths: ["/dashboard/manager/members"] }
  );
}

/**
 * Salona gelen üyelik isteklerini getirir
 */
export async function getMembershipRequestsByGymId(
  gymId: string
): Promise<ApiResponse> {
  if (!gymId) {
    return { success: false, error: "Salon ID'si gereklidir." };
  }

  return await createAction(
    async (_, supabase, authUserId) => {
      // Salon yöneticisi yetkisi kontrolü
      const { data: gym } = await supabase
        .from("gyms")
        .select("manager_user_id")
        .eq("id", gymId)
        .single();

      if (!gym || gym.manager_user_id !== authUserId) {
        throw new Error(
          "Bu salonun üyelik isteklerini görüntülemek için yetkiniz bulunmuyor."
        );
      }

      const { data, error } = await supabase
        .from("memberships")
        .select(
          `
        *,
        user:users(*)
      `
        )
        .eq("gym_id", gymId)
        .eq("status", "pending_approval");

      if (error) {
        throw new Error(
          `Salon üyelik istekleri getirilirken hata: ${error.message}`
        );
      }

      return data || [];
    },
    { requireAuth: true, revalidatePaths: ["/dashboard/manager/members"] }
  );
}

/**
 * Yöneticinin tüm salonlarına gelen üyelik isteklerini getirir
 */
export async function getAllMembershipRequestsByManagerId(
  managerId: string
): Promise<ApiResponse> {
  if (!managerId) {
    return { success: false, error: "Yönetici ID'si gereklidir." };
  }

  return await createAction(
    async (_, supabase, authUserId) => {
      // Kullanıcı yetki kontrolü
      if (authUserId !== managerId) {
        throw new Error(
          "Bu salonların üyelik isteklerini görüntülemek için yetkiniz bulunmuyor."
        );
      }

      // Yöneticinin salonlarını al
      const { data: gyms, error: gymsError } = await supabase
        .from("gyms")
        .select("id")
        .eq("manager_user_id", managerId);

      if (gymsError || !gyms || gyms.length === 0) {
        return [];
      }

      const gymIds = gyms.map((gym: { id: string }) => gym.id);

      // Tüm salonlardaki istekleri getir
      const { data, error } = await supabase
        .from("memberships")
        .select(
          `
        *,
        user:users(id, name, surname, email, profile_picture_url),
        gym:gyms(id, name)
      `
        )
        .in("gym_id", gymIds)
        .eq("status", "pending_approval");

      if (error) {
        throw new Error(`Üyelik istekleri getirilirken hata: ${error.message}`);
      }

      return data || [];
    },
    { requireAuth: true, revalidatePaths: ["/dashboard/manager/members"] }
  );
}

/**
 * Üyelik durumunu günceller
 */
export async function updateMembershipStatus(
  membershipId: string,
  status: string
): Promise<ApiResponse> {
  if (!membershipId) {
    return { success: false, error: "Üyelik ID'si gereklidir." };
  }

  // Geçerli durum değerlerini kontrol et
  const validStatuses = [
    "active",
    "approved_passive",
    "pending_approval",
    "rejected",
  ];
  if (!validStatuses.includes(status)) {
    return { success: false, error: "Geçersiz durum değeri." };
  }

  return await createAction(
    async (_, supabase, userId) => {
      if (!userId) {
        throw new Error("Bu işlem için giriş yapmanız gerekiyor.");
      }

      // İlk olarak bu üyeliğin ait olduğu spor salonunu bulalım
      const { data: membership, error: membershipError } = await supabase
        .from("memberships")
        .select("gym_id")
        .eq("id", membershipId)
        .single();

      if (membershipError || !membership) {
        throw new Error("Üyelik bulunamadı.");
      }

      // Sonra bu salon üzerinde yetki kontrolü yapalım
      const { data: gym, error: gymError } = await supabase
        .from("gyms")
        .select("manager_user_id")
        .eq("id", membership.gym_id)
        .single();

      if (gymError || !gym) {
        throw new Error("Salon bulunamadı.");
      }

      // Yetki kontrolü
      if (userId !== gym.manager_user_id) {
        throw new Error("Bu üyeliği güncellemek için yetkiniz yok.");
      }

      // Üyelik durumunu güncelle
      const { error: updateError } = await supabase
        .from("memberships")
        .update({
          status: status,
          updated_at: new Date().toISOString(),
        })
        .eq("id", membershipId);

      if (updateError) {
        throw new Error(`Üyelik güncellenirken hata: ${updateError.message}`);
      }

      return { message: "Üyelik durumu başarıyla güncellendi." };
    },
    {
      requireAuth: true,
      revalidatePaths: ["/dashboard/manager/[gymId]/members"],
    }
  );
}
