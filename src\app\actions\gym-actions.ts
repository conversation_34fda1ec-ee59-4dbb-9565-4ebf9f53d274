"use server";

import { createAction, validateFormData } from "@/lib/actions/core";
import { ApiResponse } from "@/lib/actions/types";
import {
  gymCreateSchema,
  gymSearchSchema,
  gymUpdateSchema,
} from "@/lib/schemas/schemas";
import type { Tables } from "@/lib/supabase/types";

/**
 * Yeni bir salon oluşturur
 */
export async function createGym(formData: FormData): Promise<ApiResponse> {
  const { data, error } = await validateFormData(formData, gymCreateSchema);

  if (error || !data) {
    return { success: false, error: error || "Geçersiz form verisi" };
  }

  const {
    userId,
    name,
    description,
    phone,
    email,
    address,
    city,
    district,
    gym_type,
  } = data;

  return await createAction(
    async (_, supabase, currentUserId) => {
      // Auth kontrolü - createAction'dan gelen userId'yi kullan
      const effectiveUserId = currentUserId || userId;

      if (!effectiveUserId) {
        throw new Error("Bu işlem için giriş yapmanız gerekiyor.");
      }

      // Kullanıcının manager olup olmadığını kontrol et
      const { data: userData, error: userError } = await supabase
        .from("users")
        .select("is_manager")
        .eq("id", effectiveUserId)
        .single();

      if (userError || !userData || !userData.is_manager) {
        throw new Error(
          "Bu işlemi gerçekleştirmek için yönetici olmanız gerekiyor."
        );
      }

      // Managers tablosundan kullanıcıya ait manager kaydını al
      const { data: managerData, error: managerError } = await supabase
        .from("managers")
        .select("user_id, status, tier")
        .eq("user_id", effectiveUserId)
        .single();

      if (managerError || !managerData) {
        throw new Error("Yönetici kaydınız bulunamadı.");
      }

      // Manager'ın aktif olup olmadığını kontrol et
      if (managerData.status !== "active") {
        throw new Error("Salon oluşturmak için aktif bir aboneliğiniz olmalı.");
      }

      // Manager'ın tier bilgisine göre maksimum salon sayısını al
      const { data: packageData, error: packageError } = await supabase
        .from("platform_packages")
        .select("max_gyms")
        .eq("tier", managerData.tier)
        .eq("is_active", true)
        .limit(1)
        .single();

      if (packageError || !packageData) {
        throw new Error("Paket bilgileriniz alınamadı.");
      }

      // Mevcut salon sayısını kontrol et
      const { data: existingGyms, error: gymsError } = await supabase
        .from("gyms")
        .select("id")
        .eq("manager_user_id", effectiveUserId);

      if (gymsError) {
        throw new Error("Mevcut salon bilgileriniz kontrol edilemedi.");
      }

      const currentGymCount = existingGyms?.length || 0;

      // Salon sayısı limitini kontrol et
      if (
        packageData.max_gyms !== null &&
        currentGymCount >= packageData.max_gyms
      ) {
        const tierNames = {
          starter: "Başlangıç",
          professional: "Profesyonel",
          enterprise: "Kurumsal",
        };

        throw new Error(
          `${
            tierNames[managerData.tier as keyof typeof tierNames]
          } paketinizle maksimum ${
            packageData.max_gyms
          } salon oluşturabilirsiniz. Daha fazla salon için paketinizi yükseltin.`
        );
      }

      // Slug oluştur
      const generateSlug = (name: string, city?: string) => {
        const baseSlug = name
          .toLowerCase()
          .replace(/[^a-z0-9\s-]/g, "") // Özel karakterleri kaldır
          .replace(/\s+/g, "-") // Boşlukları tire ile değiştir
          .replace(/-+/g, "-") // Çoklu tireleri tek tire yap
          .trim();

        return city
          ? `${baseSlug}-${city.toLowerCase().replace(/\s+/g, "-")}`
          : baseSlug;
      };

      const baseSlug = generateSlug(name, city);

      // Slug'ın benzersiz olup olmadığını kontrol et
      let finalSlug = baseSlug;
      let counter = 1;

      while (true) {
        const { data: existingSlug } = await supabase
          .from("gyms")
          .select("id")
          .eq("slug", finalSlug)
          .maybeSingle();

        if (!existingSlug) break;

        finalSlug = `${baseSlug}-${counter}`;
        counter++;
      }

      // Yeni salon oluştur - created_at ve updated_at'ı Supabase'e bırak
      const { data: gymData, error: gymError } = await supabase
        .from("gyms")
        .insert({
          manager_user_id: effectiveUserId,
          name,
          description,
          gym_phone: phone,
          email,
          address,
          city,
          district,
          gym_type,
          slug: finalSlug,
          status: "active",
        })
        .select()
        .single();

      if (gymError) {
        console.error("Gym creation error:", gymError);
        throw new Error(
          gymError.message || "Salon oluşturulurken bir hata oluştu."
        );
      }

      return gymData;
    },
    {
      requireAuth: true,
      revalidatePaths: ["/dashboard/manager", "/settings", "/gym-setup"],
    }
  );
}

/**
 * Salon bilgilerini slug'a göre getirir
 */
export async function getGymBySlug(
  slug: string
): Promise<ApiResponse<Tables<"gyms">>> {
  if (!slug) {
    return { success: false, error: "Salon slug'ı gereklidir." };
  }

  return await createAction(async (_, supabase, userId) => {
    // Önce gym verisini çekelim
    const { data, error } = await supabase
      .from("gyms")
      .select(
        "id, name, description, address, city, district, gym_phone, email, gym_type, logo_url, cover_image_url, features, status, manager_user_id, slug, created_at, updated_at"
      )
      .eq("slug", slug)
      .single();

    if (error) {
      throw new Error(`Salon bilgileri alınamadı: ${error.message}`);
    }

    // Eğer kullanıcı giriş yapmışsa ve yetkisi varsa kontrol etmeliyiz
    if (userId) {
      // Eğer kullanıcı bir yönetici ise ve bu salon onun değilse
      // ve salon durumu active değilse erişimi reddet
      if (data.status !== "active" && userId !== data.manager_user_id) {
        throw new Error("Bu salona erişim yetkiniz yok.");
      }
    } else if (data.status !== "active") {
      // Giriş yapmayan kullanıcılar sadece aktif salonları görebilir
      throw new Error("Bu salon şu anda aktif değil.");
    }

    return data;
  });
}

/**
 * Yöneticiye ait tüm salonları getirir
 */
export async function getGymsByManagerId(
  managerUserId: string
): Promise<ApiResponse<Tables<"gyms">[]>> {
  if (!managerUserId) {
    return { success: false, error: "Yönetici ID'si gereklidir." };
  }

  return await createAction(async (_, supabase) => {
    const { data, error } = await supabase
      .from("gyms")
      .select("id, name, city, district, address, logo_url, status, created_at")
      .eq("manager_user_id", managerUserId);

    if (error) {
      throw new Error(`Salonlar alınamadı: ${error.message}`);
    }

    return data || [];
  });
}

/**
 * Kullanıcının yönetici olduğu tüm salonları getirir (giriş yapmış kullanıcı için)
 */
export async function getMyGyms(): Promise<ApiResponse<Tables<"gyms">[]>> {
  return await createAction(
    async (_, supabase, userId) => {
      if (!userId) {
        throw new Error("Bu işlem için giriş yapmanız gerekiyor.");
      }

      // Önce kullanıcının manager olup olmadığını kontrol et
      const { data: userData, error: userError } = await supabase
        .from("users")
        .select("is_manager")
        .eq("id", userId)
        .single();

      if (userError || !userData || !userData.is_manager) {
        throw new Error("Yönetici yetkiniz bulunmuyor.");
      }

      const { data, error } = await supabase
        .from("gyms")
        .select(
          "id, name, logo_url, city, district, address, status, slug, created_at"
        )
        .eq("manager_user_id", userId);

      if (error) {
        throw new Error(`Salonlar alınamadı: ${error.message}`);
      }

      return data || [];
    },
    { requireAuth: true }
  );
}

/**
 * Salon bilgilerini günceller
 */
export async function updateGym(
  formData: FormData
): Promise<ApiResponse<Tables<"gyms">>> {
  const { data, error } = await validateFormData(formData, gymUpdateSchema);

  if (error || !data) {
    return { success: false, error: error || "Geçersiz form verisi" };
  }

  const { gymId, ...updateData } = data;

  return await createAction(
    async (_, supabase, userId) => {
      if (!userId) {
        throw new Error("Bu işlem için giriş yapmanız gerekiyor.");
      }

      // Salon sahibi olup olmadığını kontrol et
      const { data: gym } = await supabase
        .from("gyms")
        .select("manager_user_id")
        .eq("id", gymId)
        .single();

      if (!gym) {
        throw new Error("Salon bulunamadı.");
      }

      // Yetki kontrolü
      if (userId !== gym.manager_user_id) {
        throw new Error("Bu salonu düzenlemek için yetkiniz yok.");
      }

      // Salon bilgilerini güncelle
      const { data: updatedGym, error: updateError } = await supabase
        .from("gyms")
        .update({
          ...updateData,
          updated_at: new Date().toISOString(),
        })
        .eq("id", gymId)
        .select()
        .single();

      if (updateError) {
        throw new Error(`Salon güncellenirken hata: ${updateError.message}`);
      }

      return updatedGym;
    },
    {
      requireAuth: true,
      revalidatePaths: [
        `/dashboard/manager/gyms/${gymId}`,
        `/gyms/${gymId}`,
        "/dashboard/manager",
      ],
    }
  );
}

/**
 * Salon ara (arama metni, şehir, özellikler filtreleriyle)
 */
export async function searchGyms(
  formData: FormData
): Promise<ApiResponse<Tables<"gyms">[]>> {
  const { data, error } = await validateFormData(formData, gymSearchSchema);

  if (error) {
    return { success: false, error };
  }

  const query = data?.query || "";
  const city = data?.city;
  const features = data?.features || [];

  return await createAction(async (_, supabase) => {
    let queryBuilder = supabase
      .from("gyms")
      .select(
        "id, name, description, city, district, address, features, logo_url, cover_image_url, slug, created_at"
      )
      .eq("status", "active");

    // Arama metni varsa uygula
    if (query) {
      queryBuilder = queryBuilder.or(
        `name.ilike.%${query}%, description.ilike.%${query}%`
      );
    }

    // Şehir filtresi varsa uygula
    if (city) {
      queryBuilder = queryBuilder.eq("city", city);
    }

    // Özellik filtreleri varsa uygula
    if (features.length > 0) {
      features.forEach((feature) => {
        queryBuilder = queryBuilder.contains("features", [feature]);
      });
    }

    const { data: gyms, error: searchError } = await queryBuilder;

    if (searchError) {
      throw new Error(`Salonlar aranırken hata: ${searchError.message}`);
    }

    return gyms || [];
  });
}

/**
 * Tüm aktif salonları getirir (findGym sayfası için)
 */
export async function getAllActiveGyms(): Promise<
  ApiResponse<Tables<"gyms">[]>
> {
  return await createAction(async (_, supabase) => {
    const { data, error } = await supabase
      .from("gyms")
      .select(
        "id, name, description, city, district, address, features, logo_url, cover_image_url, slug, created_at"
      )
      .eq("status", "active")
      .order("created_at", { ascending: false });

    if (error) {
      throw new Error(`Salonlar getirilirken hata: ${error.message}`);
    }

    return data || [];
  });
}

/**
 * Salon arama parametreleriyle filtreleme (client-side kullanım için)
 */
export async function searchGymsWithParams(params: {
  query?: string;
  city?: string;
  features?: string[];
}): Promise<ApiResponse<Tables<"gyms">[]>> {
  const { query = "", city, features = [] } = params;

  return await createAction(async (_, supabase) => {
    let queryBuilder = supabase
      .from("gyms")
      .select(
        "id, name, description, city, district, address, features, logo_url, cover_image_url, slug, created_at"
      )
      .eq("status", "active");

    // Arama metni varsa uygula
    if (query.trim()) {
      queryBuilder = queryBuilder.or(
        `name.ilike.%${query}%, description.ilike.%${query}%, address.ilike.%${query}%, city.ilike.%${query}%, district.ilike.%${query}%`
      );
    }

    // Şehir filtresi varsa uygula
    if (city) {
      queryBuilder = queryBuilder.eq("city", city);
    }

    // Özellik filtreleri varsa uygula
    if (features.length > 0) {
      features.forEach((feature) => {
        queryBuilder = queryBuilder.contains("features", [feature]);
      });
    }

    queryBuilder = queryBuilder.order("created_at", { ascending: false });

    const { data: gyms, error: searchError } = await queryBuilder;

    if (searchError) {
      throw new Error(`Salonlar aranırken hata: ${searchError.message}`);
    }

    return gyms || [];
  });
}

/**
 * Salon istatistiklerini getirir (paket sayısı, değerlendirme ortalaması vb.)
 */
export async function getGymStatsForListing(gymId: string): Promise<
  ApiResponse<{
    packageCount: number;
    averageRating: number;
    reviewCount: number;
  }>
> {
  return await createAction(async (_, supabase) => {
    // Paket sayısını getir
    const { count: packageCount } = await supabase
      .from("gym_packages")
      .select("*", { count: "exact", head: true })
      .eq("gym_id", gymId)
      .eq("is_active", true);

    // Değerlendirme istatistiklerini getir
    const { data: reviewStats } = await supabase
      .from("reviews")
      .select("rating")
      .eq("gym_id", gymId);

    const reviewCount = reviewStats?.length || 0;
    const averageRating =
      reviewCount > 0
        ? reviewStats.reduce(
            (sum: number, review: any) => sum + review.rating,
            0
          ) / reviewCount
        : 0;

    return {
      packageCount: packageCount || 0,
      averageRating: Math.round(averageRating * 10) / 10, // 1 ondalık basamak
      reviewCount,
    };
  });
}

/**
 * Tüm benzersiz şehirleri getirir (filtre için)
 */
export async function getUniqueCities(): Promise<ApiResponse<string[]>> {
  return await createAction<string[]>(async (_, supabase) => {
    const { data, error } = await supabase
      .from("gyms")
      .select("city")
      .eq("status", "active")
      .not("city", "is", null);

    if (error) {
      throw new Error(`Şehirler getirilirken hata: ${error.message}`);
    }

    // Benzersiz şehirleri filtrele ve sırala
    const uniqueCities = [
      ...new Set(data?.map((gym: any) => gym.city).filter(Boolean)),
    ].sort();

    return uniqueCities as string[];
  });
}

/**
 * Tüm benzersiz özellikleri getirir (filtre için)
 */
export async function getUniqueFeatures(): Promise<ApiResponse<string[]>> {
  return await createAction<string[]>(async (_, supabase) => {
    const { data, error } = await supabase
      .from("gyms")
      .select("features")
      .eq("status", "active")
      .not("features", "is", null);

    if (error) {
      throw new Error(`Özellikler getirilirken hata: ${error.message}`);
    }

    // Tüm özellikleri birleştir ve benzersiz olanları al
    const allFeatures = data?.flatMap((gym: any) => gym.features || []) || [];
    const uniqueFeatures = [...new Set(allFeatures)].sort();

    return uniqueFeatures as string[];
  });
}

/**
 * Salon logosunu günceller
 */
export async function updateGymLogo(formData: FormData): Promise<ApiResponse> {
  const gymId = formData.get("gymId") as string;
  const logoFile = formData.get("logo") as File;

  if (!gymId || !logoFile) {
    return { success: false, error: "Salon ID'si veya logo dosyası eksik" };
  }

  return await createAction(
    async (_, supabase, userId) => {
      if (!userId) {
        throw new Error("Bu işlem için giriş yapmanız gerekiyor.");
      }

      // Salon sahibi olup olmadığını kontrol et
      const { data: gym } = await supabase
        .from("gyms")
        .select("manager_user_id")
        .eq("id", gymId)
        .single();

      if (!gym) {
        throw new Error("Salon bulunamadı.");
      }

      // Yetki kontrolü
      if (userId !== gym.manager_user_id) {
        throw new Error("Bu salonu düzenlemek için yetkiniz yok.");
      }

      // Dosya adı oluştur
      const fileExt = logoFile.name.split(".").pop();
      const fileName = `${gymId}_${Date.now()}.${fileExt}`;

      // Dosyayı yükle
      const { error: storageError } = await supabase.storage
        .from("gym-logos")
        .upload(fileName, logoFile, {
          cacheControl: "3600",
          upsert: true,
        });

      if (storageError) {
        throw new Error(`Logo yüklenirken hata: ${storageError.message}`);
      }

      // Genel URL'i al
      const { data: urlData } = await supabase.storage
        .from("gym-logos")
        .getPublicUrl(fileName);

      const publicUrl = urlData.publicUrl;

      // Salon bilgisini güncelle
      const { data: updatedGym, error: updateError } = await supabase
        .from("gyms")
        .update({
          logo_url: publicUrl,
          updated_at: new Date().toISOString(),
        })
        .eq("id", gymId)
        .select()
        .single();

      if (updateError) {
        throw new Error(
          `Salon logosu güncellenirken hata: ${updateError.message}`
        );
      }

      return updatedGym;
    },
    {
      requireAuth: true,
      revalidatePaths: [`/dashboard/manager/gyms/${gymId}`, `/gyms/${gymId}`],
    }
  );
}

/**
 * Salon verilerini dışa aktarır (JSON formatında)
 */
export async function exportGymData(gymId: string): Promise<
  ApiResponse<{
    gym: Tables<"gyms">;
    members: any[];
    memberships: any[];
    packages: any[];
    attendance: any[];
    exportDate: string;
  }>
> {
  return await createAction(
    async (_, supabase, userId) => {
      if (!userId) {
        throw new Error("Bu işlem için giriş yapmanız gerekiyor.");
      }

      // Salon sahibi olup olmadığını kontrol et
      const { data: gym, error: gymError } = await supabase
        .from("gyms")
        .select("*")
        .eq("id", gymId)
        .eq("manager_user_id", userId)
        .single();

      if (gymError || !gym) {
        throw new Error("Salon bulunamadı veya erişim yetkiniz yok.");
      }

      // Üye bilgilerini al
      const { data: memberships, error: membershipsError } = await supabase
        .from("memberships")
        .select(
          `
          *,
          users:user_id (
            id,
            email,
            full_name,
            phone,
            created_at
          )
        `
        )
        .eq("gym_id", gymId);

      if (membershipsError) {
        console.warn("Üye bilgileri alınamadı:", membershipsError.message);
      }

      // Paket bilgilerini al
      const { data: packages, error: packagesError } = await supabase
        .from("gym_packages")
        .select("*")
        .eq("gym_id", gymId);

      if (packagesError) {
        console.warn("Paket bilgileri alınamadı:", packagesError.message);
      }

      // Devam kayıtlarını al (son 6 ay)
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

      const { data: attendance, error: attendanceError } = await supabase
        .from("attendance_records")
        .select("*")
        .eq("gym_id", gymId)
        .gte("check_in_time", sixMonthsAgo.toISOString());

      if (attendanceError) {
        console.warn("Devam kayıtları alınamadı:", attendanceError.message);
      }

      return {
        gym,
        members:
          memberships?.map((m: any) => ({
            ...m.users,
            membership: {
              start_date: m.start_date,
              end_date: m.end_date,
              status: m.status,
              package_type: m.package_type,
            },
          })) || [],
        memberships: memberships || [],
        packages: packages || [],
        attendance: attendance || [],
        exportDate: new Date().toISOString(),
      };
    },
    { requireAuth: true }
  );
}

/**
 * Salonu pasif yapar (soft disable)
 */
export async function deactivateGym(
  gymId: string,
  reason: string
): Promise<ApiResponse<Tables<"gyms">>> {
  return await createAction(
    async (_, supabase, userId) => {
      if (!userId) {
        throw new Error("Bu işlem için giriş yapmanız gerekiyor.");
      }

      // Salon sahibi kontrolü
      const { data: gym } = await supabase
        .from("gyms")
        .select("manager_user_id")
        .eq("id", gymId)
        .single();

      if (!gym || gym.manager_user_id !== userId) {
        throw new Error("Bu salonu pasif yapmak için yetkiniz yok.");
      }

      const { data, error } = await supabase
        .from("gyms")
        .update({
          status: "inactive",
          deactivation_reason: reason,
          deactivated_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .eq("id", gymId)
        .select()
        .single();

      if (error) {
        throw new Error(`Salon pasif yapılırken hata oluştu: ${error.message}`);
      }

      return data;
    },
    { requireAuth: true, revalidatePaths: ["/dashboard/manager/gyms"] }
  );
}

/**
 * Salonu soft delete yapar (30 gün geri alma süresi)
 */
export async function softDeleteGym(
  gymId: string,
  reason: string,
  deleteReason: string
): Promise<ApiResponse<Tables<"gyms">>> {
  return await createAction(
    async (_, supabase, userId) => {
      if (!userId) {
        throw new Error("Bu işlem için giriş yapmanız gerekiyor.");
      }

      // Salon sahibi kontrolü
      const { data: gym } = await supabase
        .from("gyms")
        .select("manager_user_id")
        .eq("id", gymId)
        .single();

      if (!gym || gym.manager_user_id !== userId) {
        throw new Error("Bu salonu silmek için yetkiniz yok.");
      }

      const deleteDate = new Date();
      const restoreDeadline = new Date();
      restoreDeadline.setDate(restoreDeadline.getDate() + 30);

      const { data, error } = await supabase
        .from("gyms")
        .update({
          status: "deleted",
          deleted_at: deleteDate.toISOString(),
          restore_deadline: restoreDeadline.toISOString(),
          deletion_reason: deleteReason,
          deactivation_reason: reason,
          updated_at: new Date().toISOString(),
        })
        .eq("id", gymId)
        .select()
        .single();

      if (error) {
        throw new Error(`Salon silinirken hata oluştu: ${error.message}`);
      }

      return data;
    },
    { requireAuth: true, revalidatePaths: ["/dashboard/manager/gyms"] }
  );
}

/**
 * Soft delete edilmiş salonu geri yükler
 */
export async function restoreGym(
  gymId: string
): Promise<ApiResponse<Tables<"gyms">>> {
  return await createAction(
    async (_, supabase, userId) => {
      if (!userId) {
        throw new Error("Bu işlem için giriş yapmanız gerekiyor.");
      }

      // Salon sahibi kontrolü ve geri yükleme süresi kontrolü
      const { data: gym } = await supabase
        .from("gyms")
        .select("manager_user_id, restore_deadline, status")
        .eq("id", gymId)
        .single();

      if (!gym || gym.manager_user_id !== userId) {
        throw new Error("Bu salonu geri yüklemek için yetkiniz yok.");
      }

      if (gym.status !== "deleted") {
        throw new Error("Bu salon silinmiş durumda değil.");
      }

      if (gym.restore_deadline && new Date() > new Date(gym.restore_deadline)) {
        throw new Error("Geri yükleme süresi dolmuş.");
      }

      const { data, error } = await supabase
        .from("gyms")
        .update({
          status: "active",
          deleted_at: null,
          restore_deadline: null,
          deletion_reason: null,
          updated_at: new Date().toISOString(),
        })
        .eq("id", gymId)
        .select()
        .single();

      if (error) {
        throw new Error(`Salon geri yüklenirken hata oluştu: ${error.message}`);
      }

      return data;
    },
    { requireAuth: true, revalidatePaths: ["/dashboard/manager/gyms"] }
  );
}

/**
 * Salon silme işlemi (hard delete - sadece admin)
 */
export async function deleteGym(gymId: string): Promise<ApiResponse> {
  if (!gymId) {
    return { success: false, error: "Salon ID'si gereklidir." };
  }

  return await createAction(
    async (_, supabase, userId) => {
      if (!userId) {
        throw new Error("Bu işlem için giriş yapmanız gerekiyor.");
      }

      // Salon sahibi olup olmadığını kontrol et
      const { data: gym } = await supabase
        .from("gyms")
        .select("manager_user_id")
        .eq("id", gymId)
        .single();

      if (!gym) {
        throw new Error("Salon bulunamadı.");
      }

      // Yetki kontrolü
      if (userId !== gym.manager_user_id) {
        throw new Error("Bu salonu silmek için yetkiniz yok.");
      }

      // Salon silme işlemi
      const { error: deleteError } = await supabase
        .from("gyms")
        .delete()
        .eq("id", gymId)
        .eq("manager_user_id", userId);

      if (deleteError) {
        throw new Error(`Salon silinirken hata: ${deleteError.message}`);
      }

      return { message: "Salon başarıyla silindi." };
    },
    { requireAuth: true, revalidatePaths: ["/dashboard/manager/gyms"] }
  );
}
