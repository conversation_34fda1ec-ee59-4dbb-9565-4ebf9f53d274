"use client";

import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Loader2, Search, UserPlus, Users } from "lucide-react";
import { getGymBySlug } from "@/app/actions/gym-actions";
import { Tables } from "@/lib/supabase/types";

export default function MembersPage() {
  const params = useParams();
  const slug = params.slug as string;
  const [gym, setGym] = useState<Tables<"gyms"> | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    async function fetchGym() {
      if (!slug) return;

      try {
        const response = await getGymBySlug(slug);
        if (response.success && response.data) {
          setGym(response.data);
        }
      } catch (error) {
        console.error("Salon bilgileri yüklenirken hata:", error);
      } finally {
        setLoading(false);
      }
    }

    fetchGym();
  }, [slug]);

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!gym) {
    return (
      <div className="text-center py-8">
        <h2 className="text-xl font-semibold">Salon bulunamadı</h2>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Üyeler</h1>
          <p className="text-muted-foreground">
            {gym.name} salonunuzun üye yönetimi
          </p>
        </div>
        <Button>
          <UserPlus className="h-4 w-4 mr-2" />
          Yeni Üye Ekle
        </Button>
      </div>

      <div className="flex items-center space-x-2">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Üye ara..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Toplam Üye</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">0</div>
            <p className="text-xs text-muted-foreground">
              Kayıtlı üye sayısı
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Aktif Üye</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">0</div>
            <p className="text-xs text-muted-foreground">
              Aktif aboneliği olan
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Bekleyen Başvuru</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">0</div>
            <p className="text-xs text-muted-foreground">
              Onay bekleyen
            </p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Üye Listesi</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-lg font-medium">Henüz üye bulunmuyor</p>
            <p className="text-sm">İlk üyenizi eklemek için "Yeni Üye Ekle" butonunu kullanın.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
