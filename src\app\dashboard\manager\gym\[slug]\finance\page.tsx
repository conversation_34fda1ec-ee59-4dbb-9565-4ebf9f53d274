"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { useAuth } from "@/components/auth/auth-provider";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  CreditCard,
  Calendar,
  Users,
  PieChart,
  BarChart3,
  Download,
  Filter,
  Loader2,
  AlertTriangle,
  CheckCircle,
  Clock,
  XCircle,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { getGymFinanceData, getGymPaymentStats } from "@/app/actions/gym-finance-actions";
import { getGymBySlug } from "@/app/actions/gym-actions";

interface GymFinanceData {
  totalRevenue: number;
  monthlyRevenue: number;
  pendingPayments: number;
  completedPayments: number;
  revenueGrowth: number;
  paymentGrowth: number;
  totalMembers: number;
  activeMembers: number;
  averageRevenuePerMember: number;
  monthlyData: {
    month: string;
    revenue: number;
    payments: number;
    newMembers: number;
  }[];
  packageRevenue: {
    packageName: string;
    revenue: number;
    memberCount: number;
    percentage: number;
  }[];
  recentTransactions: {
    id: string;
    memberName: string;
    amount: number;
    status: "completed" | "pending" | "failed";
    date: string;
    packageName: string;
    paymentMethod: string;
  }[];
}

export default function GymFinancePage() {
  const { authUser } = useAuth();
  const params = useParams();
  const gymSlug = params?.slug as string;

  const [financeData, setFinanceData] = useState<GymFinanceData | null>(null);
  const [gymInfo, setGymInfo] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState<"month" | "quarter" | "year">("month");

  useEffect(() => {
    const loadFinanceData = async () => {
      if (!authUser || !gymSlug) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // Salon bilgilerini al
        const gymResponse = await getGymBySlug(gymSlug);
        if (!gymResponse.success || !gymResponse.data) {
          throw new Error("Salon bulunamadı");
        }
        setGymInfo(gymResponse.data);

        // Finansal verileri al
        const financeResponse = await getGymFinanceData(gymResponse.data.id);
        if (!financeResponse.success || !financeResponse.data) {
          throw new Error(financeResponse.error || "Finansal veriler alınamadı");
        }
        setFinanceData(financeResponse.data);

      } catch (err: any) {
        console.error("Error loading finance data:", err);
        setError(err.message || "Finansal veriler yüklenirken bir hata oluştu.");
      } finally {
        setIsLoading(false);
      }
    };

    loadFinanceData();
  }, [authUser, gymSlug, selectedPeriod]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("tr-TR", {
      style: "currency",
      currency: "TRY",
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value > 0 ? "+" : ""}${value.toFixed(1)}%`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("tr-TR", {
      day: "numeric",
      month: "short",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getPaymentStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case "failed":
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getPaymentStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge className="bg-green-100 text-green-800">Tamamlandı</Badge>;
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-800">Beklemede</Badge>;
      case "failed":
        return <Badge className="bg-red-100 text-red-800">Başarısız</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Finansal veriler yükleniyor...</span>
        </div>
      </div>
    );
  }

  if (!authUser) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          Finansal verileri görüntülemek için giriş yapmanız gerekiyor.
        </AlertDescription>
      </Alert>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <DollarSign className="h-8 w-8" />
            Finansal Özet
          </h1>
          <p className="text-muted-foreground">
            {gymInfo?.name} - Gelir analizi ve ödeme durumları
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" className="gap-2">
            <Filter className="h-4 w-4" />
            Filtrele
          </Button>
          <Button className="gap-2">
            <Download className="h-4 w-4" />
            Rapor İndir
          </Button>
        </div>
      </div>

      {/* Period Selection */}
      <div className="flex gap-2">
        {[
          { key: "month", label: "Bu Ay" },
          { key: "quarter", label: "Bu Çeyrek" },
          { key: "year", label: "Bu Yıl" },
        ].map((period) => (
          <Button
            key={period.key}
            variant={selectedPeriod === period.key ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedPeriod(period.key as any)}
          >
            {period.label}
          </Button>
        ))}
      </div>

      {financeData && (
        <>
          {/* Key Financial Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">Toplam Gelir</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(financeData.totalRevenue)}</div>
                <div className="flex items-center gap-1 text-xs">
                  {financeData.revenueGrowth > 0 ? (
                    <TrendingUp className="h-3 w-3 text-green-500" />
                  ) : (
                    <TrendingDown className="h-3 w-3 text-red-500" />
                  )}
                  <span className={cn(
                    financeData.revenueGrowth > 0 ? "text-green-500" : "text-red-500"
                  )}>
                    {formatPercentage(financeData.revenueGrowth)}
                  </span>
                  <span className="text-muted-foreground">önceki aya göre</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">Aylık Gelir</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(financeData.monthlyRevenue)}</div>
                <p className="text-xs text-muted-foreground">
                  Bu ayki toplam gelir
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">Aktif Üye</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{financeData.activeMembers}</div>
                <p className="text-xs text-muted-foreground">
                  Toplam {financeData.totalMembers} üyeden
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">Üye Başına Ortalama</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(financeData.averageRevenuePerMember)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Ortalama gelir
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Payment Status */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">Tamamlanan Ödemeler</CardTitle>
                <CheckCircle className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {formatCurrency(financeData.completedPayments)}
                </div>
                <div className="flex items-center gap-1 text-xs">
                  <TrendingUp className="h-3 w-3 text-green-500" />
                  <span className="text-green-500">
                    {formatPercentage(financeData.paymentGrowth)}
                  </span>
                  <span className="text-muted-foreground">artış</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">Bekleyen Ödemeler</CardTitle>
                <Clock className="h-4 w-4 text-yellow-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-yellow-600">
                  {formatCurrency(financeData.pendingPayments)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Onay bekleyen ödemeler
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">Ödeme Başarı Oranı</CardTitle>
                <PieChart className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {financeData.pendingPayments + financeData.completedPayments > 0 
                    ? Math.round((financeData.completedPayments / (financeData.pendingPayments + financeData.completedPayments)) * 100)
                    : 0}%
                </div>
                <p className="text-xs text-muted-foreground">
                  Başarılı ödeme oranı
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Monthly Performance & Package Revenue */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Monthly Performance */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Aylık Performans
                </CardTitle>
                <CardDescription>
                  Son 3 ayın gelir ve üye analizi
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {financeData.monthlyData.map((month, index) => (
                    <div key={index} className="flex items-center justify-between p-3 rounded-lg border">
                      <div>
                        <p className="font-medium">{month.month}</p>
                        <p className="text-sm text-muted-foreground">
                          {month.payments} ödeme • {month.newMembers} yeni üye
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold">{formatCurrency(month.revenue)}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Package Revenue */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5" />
                  Paket Bazında Gelir
                </CardTitle>
                <CardDescription>
                  Paketlere göre gelir dağılımı
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {financeData.packageRevenue.slice(0, 5).map((pkg, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="h-3 w-3 rounded-full bg-primary" style={{
                          backgroundColor: `hsl(${index * 60}, 70%, 50%)`
                        }} />
                        <div>
                          <p className="text-sm font-medium">{pkg.packageName}</p>
                          <p className="text-xs text-muted-foreground">{pkg.memberCount} üye</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-semibold">{formatCurrency(pkg.revenue)}</p>
                        <p className="text-xs text-muted-foreground">%{pkg.percentage.toFixed(1)}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Transactions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Son İşlemler
              </CardTitle>
              <CardDescription>
                En son gerçekleşen ödeme işlemleri
              </CardDescription>
            </CardHeader>
            <CardContent>
              {financeData.recentTransactions.length === 0 ? (
                <div className="text-center py-8">
                  <CreditCard className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-lg font-medium text-muted-foreground">Henüz işlem yok</p>
                  <p className="text-sm text-muted-foreground">
                    Ödeme işlemleri burada görünecek
                  </p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Üye</TableHead>
                        <TableHead>Paket</TableHead>
                        <TableHead>Tutar</TableHead>
                        <TableHead>Durum</TableHead>
                        <TableHead>Tarih</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {financeData.recentTransactions.map((transaction) => (
                        <TableRow key={transaction.id}>
                          <TableCell className="font-medium">
                            {transaction.memberName}
                          </TableCell>
                          <TableCell>{transaction.packageName}</TableCell>
                          <TableCell>{formatCurrency(transaction.amount)}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              {getPaymentStatusIcon(transaction.status)}
                              {getPaymentStatusBadge(transaction.status)}
                            </div>
                          </TableCell>
                          <TableCell>{formatDate(transaction.date)}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}
