"use client";

import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/ui/use-toast";
import {
  Setting<PERSON>,
  Edit,
  Trash2,
  Download,
  Loader2,
  Save,
  AlertTriangle,
  Power,
  RotateCcw,
  Clock,
  ArrowLeft,
} from "lucide-react";
import Link from "next/link";
import { Tables } from "@/lib/supabase/types";
import {
  getGymBySlug,
  updateGym,
  exportGymData,
  deactivateGym,
  softDeleteGym,
} from "@/app/actions/gym-actions";
import { createClient } from "@/utils/supabase/client";
import { useAuth } from "@/components/auth/auth-provider";

export default function GymSettingsPage() {
  const params = useParams();
  const router = useRouter();
  const slug = params.slug as string;
  const { user: authUser } = useAuth();
  const { toast } = useToast();
  const supabase = createClient();

  const [gym, setGym] = useState<Tables<"gyms"> | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [isExporting, setIsExporting] = useState(false);

  // Form state
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    gym_phone: "",
    email: "",
    address: "",
    city: "",
    district: "",
    gym_type: "",
  });

  // Delete dialog state
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [selectedAction, setSelectedAction] = useState<
    "deactivate" | "soft-delete" | "hard-delete"
  >("deactivate");
  const [deleteReason, setDeleteReason] = useState("");
  const [customReason, setCustomReason] = useState("");
  const [deleteLoading, setDeleteLoading] = useState(false);

  useEffect(() => {
    fetchGymData();
  }, [slug]);

  const fetchGymData = async () => {
    try {
      setLoading(true);
      const response = await getGymBySlug(slug);

      if (response.success && response.data) {
        setGym(response.data);
        setFormData({
          name: response.data.name || "",
          description: response.data.description || "",
          gym_phone: response.data.gym_phone || "",
          email: response.data.email || "",
          address: response.data.address || "",
          city: response.data.city || "",
          district: response.data.district || "",
          gym_type: response.data.gym_type || "",
        });
      } else {
        toast({
          title: "Hata",
          description: response.error || "Salon bilgileri alınamadı",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error fetching gym:", error);
      toast({
        title: "Hata",
        description: "Salon bilgileri alınırken bir hata oluştu",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSave = async () => {
    if (!gym) return;

    try {
      setSaving(true);

      const formDataObj = new FormData();
      formDataObj.append("gymId", gym.id);
      Object.entries(formData).forEach(([key, value]) => {
        formDataObj.append(key, value);
      });

      const response = await updateGym(formDataObj);

      if (response.success) {
        toast({
          title: "Başarılı",
          description: "Salon bilgileri güncellendi",
        });
        fetchGymData(); // Refresh data
      } else {
        toast({
          title: "Hata",
          description: response.error || "Salon güncellenirken bir hata oluştu",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error updating gym:", error);
      toast({
        title: "Hata",
        description: "Salon güncellenirken bir hata oluştu",
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  const handleExportData = async () => {
    if (!gym) return;

    try {
      setIsExporting(true);
      const response = await exportGymData(gym.id);

      if (response.success) {
        toast({
          title: "Başarılı",
          description: "Salon verileri dışa aktarıldı",
        });
      } else {
        toast({
          title: "Hata",
          description:
            response.error || "Veriler dışa aktarılırken bir hata oluştu",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error exporting data:", error);
      toast({
        title: "Hata",
        description: "Veriler dışa aktarılırken bir hata oluştu",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };

  const handleDeleteAction = async () => {
    if (!gym || !authUser) return;

    try {
      setDeleteLoading(true);
      const finalReason =
        deleteReason === "other" ? customReason : deleteReason;
      let response;

      switch (selectedAction) {
        case "deactivate":
          response = await deactivateGym(gym.id, finalReason);
          break;
        case "soft-delete":
          response = await softDeleteGym(
            gym.id,
            finalReason,
            "User requested deletion"
          );
          break;
        case "hard-delete":
          // Hard delete - direkt supabase kullan
          const { error } = await supabase
            .from("gyms")
            .delete()
            .eq("id", gym.id)
            .eq("manager_user_id", authUser.id);

          if (error) {
            throw new Error(`Salon silinirken hata: ${error.message}`);
          }
          response = { success: true };
          break;
        default:
          throw new Error("Geçersiz işlem türü");
      }

      if (!response.success) {
        throw new Error(response.error || "İşlem başarısız");
      }

      toast({
        title: "Başarılı",
        description: getSuccessMessage(selectedAction),
      });

      setShowDeleteDialog(false);

      // Redirect to gyms page
      router.push("/dashboard/manager/gyms");
    } catch (error: any) {
      console.error("Delete action error:", error);
      toast({
        title: "Hata",
        description: error.message || "İşlem sırasında bir hata oluştu",
        variant: "destructive",
      });
    } finally {
      setDeleteLoading(false);
    }
  };

  const getSuccessMessage = (action: string) => {
    switch (action) {
      case "deactivate":
        return "Salon pasif hale getirildi";
      case "soft-delete":
        return "Salon silindi (30 gün içinde geri yüklenebilir)";
      case "hard-delete":
        return "Salon kalıcı olarak silindi";
      default:
        return "İşlem tamamlandı";
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-32">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!gym) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Salon bulunamadı</p>
        <Button asChild className="mt-4">
          <Link href="/dashboard/manager/gyms">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Salonlara Dön
          </Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Button variant="outline" size="sm" asChild>
            <Link href="/dashboard/manager/gyms">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Salonlara Dön
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{gym.name} - Ayarlar</h1>
            <p className="text-muted-foreground">Salon ayarlarını yönetin</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={handleExportData}
            disabled={isExporting}
          >
            {isExporting ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <Download className="h-4 w-4 mr-2" />
            )}
            Dışa Aktar
          </Button>
        </div>
      </div>

      <Tabs defaultValue="general" className="space-y-6">
        <TabsList>
          <TabsTrigger value="general">Genel Bilgiler</TabsTrigger>
          <TabsTrigger value="danger">Tehlikeli İşlemler</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Edit className="h-5 w-5" />
                Salon Bilgileri
              </CardTitle>
              <CardDescription>
                Salonunuzun temel bilgilerini düzenleyin
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Salon Adı *</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="Örn: Fitness Plus"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="gym_type">Salon Türü *</Label>
                  <Select
                    value={formData.gym_type}
                    onValueChange={(value) =>
                      handleSelectChange("gym_type", value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Salon türünü seçin" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="fitness">Fitness</SelectItem>
                      <SelectItem value="crossfit">CrossFit</SelectItem>
                      <SelectItem value="yoga">Yoga</SelectItem>
                      <SelectItem value="pilates">Pilates</SelectItem>
                      <SelectItem value="martial_arts">
                        Dövüş Sanatları
                      </SelectItem>
                      <SelectItem value="swimming">Yüzme</SelectItem>
                      <SelectItem value="other">Diğer</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Açıklama</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="Salonunuz hakkında kısa bir açıklama..."
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="gym_phone">Telefon</Label>
                  <Input
                    id="gym_phone"
                    name="gym_phone"
                    value={formData.gym_phone}
                    onChange={handleInputChange}
                    placeholder="0555 123 45 67"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">E-posta</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="address">Adres</Label>
                <Textarea
                  id="address"
                  name="address"
                  value={formData.address}
                  onChange={handleInputChange}
                  placeholder="Salon adresi..."
                  rows={2}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="city">Şehir</Label>
                  <Input
                    id="city"
                    name="city"
                    value={formData.city}
                    onChange={handleInputChange}
                    placeholder="İstanbul"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="district">İlçe</Label>
                  <Input
                    id="district"
                    name="district"
                    value={formData.district}
                    onChange={handleInputChange}
                    placeholder="Kadıköy"
                  />
                </div>
              </div>

              <div className="flex justify-end pt-4">
                <Button onClick={handleSave} disabled={saving}>
                  {saving ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  Değişiklikleri Kaydet
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="danger" className="space-y-6">
          <Card className="border-red-200 dark:border-red-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-red-600 dark:text-red-400">
                <AlertTriangle className="h-5 w-5" />
                Tehlikeli İşlemler
              </CardTitle>
              <CardDescription>
                Bu işlemler geri alınamaz olabilir. Dikkatli olun.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <AlertDialog
                open={showDeleteDialog}
                onOpenChange={setShowDeleteDialog}
              >
                <AlertDialogTrigger asChild>
                  <Button variant="destructive" className="w-full">
                    <Trash2 className="h-4 w-4 mr-2" />
                    Salonu Sil / Pasif Yap
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent className="max-w-md">
                  <AlertDialogHeader>
                    <AlertDialogTitle className="flex items-center gap-2">
                      <AlertTriangle className="h-5 w-5 text-red-500" />
                      Salon İşlemi
                    </AlertDialogTitle>
                    <AlertDialogDescription>
                      <strong>{gym.name}</strong> salonu için yapmak istediğiniz
                      işlemi seçin:
                    </AlertDialogDescription>
                  </AlertDialogHeader>

                  <div className="space-y-4">
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="deactivate"
                          name="action"
                          value="deactivate"
                          checked={selectedAction === "deactivate"}
                          onChange={(e) =>
                            setSelectedAction(e.target.value as any)
                          }
                          className="text-primary"
                        />
                        <label htmlFor="deactivate" className="text-sm">
                          <div className="flex items-center gap-2">
                            <Power className="h-4 w-4 text-orange-500" />
                            <span className="font-medium">Pasif Yap</span>
                          </div>
                          <p className="text-xs text-muted-foreground ml-6">
                            Salon geçici olarak devre dışı bırakılır
                          </p>
                        </label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="soft-delete"
                          name="action"
                          value="soft-delete"
                          checked={selectedAction === "soft-delete"}
                          onChange={(e) =>
                            setSelectedAction(e.target.value as any)
                          }
                          className="text-primary"
                        />
                        <label htmlFor="soft-delete" className="text-sm">
                          <div className="flex items-center gap-2">
                            <RotateCcw className="h-4 w-4 text-yellow-500" />
                            <span className="font-medium">Geçici Sil</span>
                          </div>
                          <p className="text-xs text-muted-foreground ml-6">
                            30 gün içinde geri yüklenebilir
                          </p>
                        </label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="hard-delete"
                          name="action"
                          value="hard-delete"
                          checked={selectedAction === "hard-delete"}
                          onChange={(e) =>
                            setSelectedAction(e.target.value as any)
                          }
                          className="text-primary"
                        />
                        <label htmlFor="hard-delete" className="text-sm">
                          <div className="flex items-center gap-2">
                            <Trash2 className="h-4 w-4 text-red-500" />
                            <span className="font-medium text-red-600">
                              Kalıcı Sil
                            </span>
                          </div>
                          <p className="text-xs text-muted-foreground ml-6">
                            Tüm veriler kalıcı olarak silinir
                          </p>
                        </label>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="reason">Sebep *</Label>
                      <Select
                        value={deleteReason}
                        onValueChange={setDeleteReason}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Sebep seçin" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="business_closure">
                            İşletme kapandı
                          </SelectItem>
                          <SelectItem value="moving">Taşınma</SelectItem>
                          <SelectItem value="cost_reduction">
                            Maliyet azaltma
                          </SelectItem>
                          <SelectItem value="dissatisfaction">
                            Memnuniyetsizlik
                          </SelectItem>
                          <SelectItem value="other">Diğer</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {deleteReason === "other" && (
                      <div className="space-y-2">
                        <Label htmlFor="customReason">Özel Sebep</Label>
                        <Textarea
                          id="customReason"
                          value={customReason}
                          onChange={(e) => setCustomReason(e.target.value)}
                          placeholder="Lütfen sebebinizi açıklayın..."
                          rows={2}
                        />
                      </div>
                    )}
                  </div>

                  <AlertDialogFooter>
                    <AlertDialogCancel>İptal</AlertDialogCancel>
                    <AlertDialogAction
                      onClick={handleDeleteAction}
                      disabled={
                        deleteLoading ||
                        !deleteReason ||
                        (deleteReason === "other" && !customReason.trim())
                      }
                      className="bg-red-600 hover:bg-red-700"
                    >
                      {deleteLoading ? (
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      ) : (
                        <Trash2 className="h-4 w-4 mr-2" />
                      )}
                      İşlemi Onayla
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
