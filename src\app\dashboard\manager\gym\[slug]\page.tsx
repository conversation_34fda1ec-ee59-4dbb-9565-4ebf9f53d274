"use client";

import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2 } from "lucide-react";
import { Tables } from "@/lib/supabase/types";
import { getGymBySlug } from "@/app/actions/gym-actions";
import { getGymStats } from "@/app/actions/dashboard-actions";

export default function ManagerGymDashboardPage() {
  const params = useParams();
  const slug = params.slug as string;
  const [gym, setGym] = useState<Tables<"gyms"> | null>(null);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    activeMembers: 0,
    totalPackages: 0,
    totalRevenue: 0,
  });

  useEffect(() => {
    async function fetchGymData() {
      if (!slug) return;

      try {
        setLoading(true);

        // Salon bilgilerini slug ile getir
        const {
          success: gymSuccess,
          data: gymData,
          error: gymError,
        } = await getGymBySlug(slug);

        if (!gymSuccess || gymError || !gymData) {
          throw new Error(gymError || "Salon bilgileri alınamadı");
        }
        setGym(gymData);

        // İstatistikleri getir (gym ID kullanarak)
        const {
          success: statsSuccess,
          data: statsData,
          error: statsError,
        } = await getGymStats(gymData.id);

        if (!statsSuccess || statsError || !statsData) {
          throw new Error(statsError || "Salon istatistikleri alınamadı");
        }

        setStats(statsData);
      } catch (error) {
        console.error("Salon verileri yüklenirken hata:", error);
      } finally {
        setLoading(false);
      }
    }

    fetchGymData();
  }, [slug]);

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!gym) {
    return (
      <div className="text-center py-8">
        <h2 className="text-xl font-semibold">Salon bulunamadı</h2>
        <p className="text-muted-foreground mt-2">
          Seçilen salon bulunamadı veya erişim izniniz yok.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Hoş Geldiniz</h1>
        <p className="text-muted-foreground">
          {gym.name} salonunuzun yönetim paneli
        </p>
        <p className="text-xs text-muted-foreground mt-1">
          URL: /dashboard/manager/gym/{gym.slug}
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Aktif Üyeler</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeMembers}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Toplam Paket</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalPackages}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Toplam Gelir</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats.totalRevenue.toLocaleString("tr-TR", {
                style: "currency",
                currency: "TRY",
              })}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-bold">Salon Bilgileri</h2>
        <div className="grid gap-4 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Genel Bilgiler</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="grid grid-cols-2">
                <span className="text-muted-foreground">İsim:</span>
                <span>{gym.name}</span>
              </div>
              <div className="grid grid-cols-2">
                <span className="text-muted-foreground">Durum:</span>
                <span>{gym.status === "active" ? "Aktif" : "Pasif"}</span>
              </div>
              <div className="grid grid-cols-2">
                <span className="text-muted-foreground">Tip:</span>
                <span>{gym.gym_type || "-"}</span>
              </div>
              <div className="grid grid-cols-2">
                <span className="text-muted-foreground">Slug:</span>
                <span className="text-xs font-mono">{gym.slug}</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>İletişim Bilgileri</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="grid grid-cols-2">
                <span className="text-muted-foreground">Adres:</span>
                <span>{gym.address || "-"}</span>
              </div>
              <div className="grid grid-cols-2">
                <span className="text-muted-foreground">Telefon:</span>
                <span>{gym.gym_phone || "-"}</span>
              </div>
              <div className="grid grid-cols-2">
                <span className="text-muted-foreground">Email:</span>
                <span>{gym.email || "-"}</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
