"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { MapPin, Phone, Star, Info, MessageSquare } from "lucide-react";
import { useAuth } from "@/components/auth/auth-provider";
import {
  requestGymMembership,
  getMembershipsByUserId,
} from "@/app/actions/membership-actions";
import { purchasePackage } from "@/app/actions/subscription-actions";
import {
  submitGymReview,
  getReviewsByGymId,
} from "@/app/actions/review-actions";
import { getGymBySlug } from "@/app/actions/gym-actions";
import { getPackagesByGymId } from "@/app/actions/package-actions";
import { getPublicAnnouncementsByGymId } from "@/app/actions/announcement-actions";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { StarRating } from "@/components/reviews/star-rating";
import type { Tables } from "@/lib/supabase/types";
import { toast } from "sonner";
import { createClient } from "@/utils/supabase/client";
import { GymDetailSkeleton } from "@/components/gym-detail-skeleton";

interface GymDetailProps {
  gymSlug: string;
}

// Gym 'features' alanı için type extension
type GymWithFeatures = Tables<"gyms"> & {
  features?: string[];
};

// GymPackages için extended type
type GymPackageExtended = Tables<"gym_packages"> & {
  currency?: string;
  price?: number; // price_amount için alias
};

export function GymDetail({ gymSlug }: GymDetailProps) {
  const { session } = useAuth();
  const user = session?.user;
  const [isLoading, setIsLoading] = useState(true);
  const [gym, setGym] = useState<GymWithFeatures | null>(null);
  const [packages, setPackages] = useState<GymPackageExtended[]>([]);
  const [reviews, setReviews] = useState<
    (Tables<"reviews"> & {
      user: {
        name: string;
        surname: string;
        profile_picture_url: string | null;
      };
    })[]
  >([]);
  const [membership, setMembership] = useState<Tables<"memberships"> | null>(
    null
  );
  const [subscriptions, setSubscriptions] = useState<Tables<"subscriptions">[]>(
    []
  );
  const [announcements, setAnnouncements] = useState<Tables<"announcements">[]>(
    []
  );
  const [requestSent, setRequestSent] = useState(false);
  const [reviewDialogOpen, setReviewDialogOpen] = useState(false);
  const [reviewRating, setReviewRating] = useState(0);
  const [reviewComment, setReviewComment] = useState("");
  const [userReview, setUserReview] = useState<Tables<"reviews"> | null>(null);
  const [averageRating, setAverageRating] = useState(0);

  useEffect(() => {
    const fetchGymData = async () => {
      if (!gymSlug) return;

      setIsLoading(true);

      try {
        // Server Action kullanarak salon bilgilerini getir
        const gymResponse = await getGymBySlug(gymSlug);

        if (!gymResponse.success || !gymResponse.data) {
          toast.error("Salon bilgileri yüklenirken bir hata oluştu");
          setIsLoading(false);
          return;
        }

        setGym(gymResponse.data as GymWithFeatures);
        const gymId = gymResponse.data.id;

        // Server Action kullanarak salon paketlerini getir
        const packagesResponse = await getPackagesByGymId(gymId);

        if (packagesResponse.success && packagesResponse.data) {
          setPackages(packagesResponse.data as GymPackageExtended[]);
        }

        // Server Action kullanarak değerlendirmeleri getir
        const reviewsResponse = await getReviewsByGymId(gymId);

        if (reviewsResponse.success && reviewsResponse.data) {
          setReviews(reviewsResponse.data.reviews);
          setAverageRating(reviewsResponse.data.averageRating);
        }

        // Server Action kullanarak duyuruları getir
        const announcementsResponse = await getPublicAnnouncementsByGymId(
          gymId
        );

        if (announcementsResponse.success && announcementsResponse.data) {
          setAnnouncements(announcementsResponse.data);
        }

        // If user is logged in, fetch membership and subscriptions
        if (user) {
          const membershipResponse = await getMembershipsByUserId(user.id);

          if (membershipResponse.success && membershipResponse.data) {
            // Find membership for this gym
            const membershipData = membershipResponse.data.find(
              (m: any) => m.membership.gym_id === gymId
            );

            if (membershipData) {
              setMembership(membershipData.membership);
              setRequestSent(true);

              // Fetch subscriptions if membership exists
              const supabase = createClient();
              const { data: subscriptionsData } = await supabase
                .from("subscriptions")
                .select(
                  `
                  *,
                  gym_package:gym_packages(*)
                `
                )
                .eq("membership_id", membershipData.membership.id);

              if (subscriptionsData) {
                setSubscriptions(subscriptionsData as any);
              }

              // Check if user has already reviewed
              const { data: userReviewData } = await supabase
                .from("reviews")
                .select(
                  "id, user_id, gym_id, rating, comment, created_at, updated_at"
                )
                .eq("user_id", user.id)
                .eq("gym_id", gymId)
                .single();

              if (userReviewData) {
                setUserReview(userReviewData);
                setReviewRating(userReviewData.rating);
                setReviewComment(userReviewData.comment || "");
              }
            }
          }
        }
      } catch (error) {
        console.error("Error fetching gym data:", error);
        toast.error("Veriler yüklenirken bir hata oluştu");
      } finally {
        setIsLoading(false);
      }
    };

    fetchGymData();
  }, [gymSlug, user]);

  const handleJoinRequest = async () => {
    if (!user || !gym) {
      toast.error("Salon üyeliği için lütfen giriş yapın veya kayıt olun.");
      return;
    }

    try {
      const result = await requestGymMembership(gym.id, user.id);

      if (result.success) {
        setRequestSent(true);
        toast.success("Başarılı", {
          description: result.message,
        });
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error("Error sending membership request:", error);
      toast.error(
        "İstek gönderilirken bir hata oluştu. Lütfen tekrar deneyin."
      );
    }
  };

  const handlePurchasePackage = async (packageId: string) => {
    if (!user || !membership) {
      toast.error(
        "Paket satın almak için önce üyelik isteği göndermeniz gerekmektedir."
      );
      return;
    }

    try {
      const result = await purchasePackage(user.id, gym?.id || "", packageId);

      if (result.success) {
        toast.success("Başarılı", {
          description: result.message,
        });

        // Refresh subscriptions and membership data
        const supabase = createClient();
        const { data: subscriptionsData } = await supabase
          .from("subscriptions")
          .select(
            `
            *,
            gym_package:gym_packages(*)
          `
          )
          .eq("membership_id", membership.id);

        if (subscriptionsData) {
          setSubscriptions(subscriptionsData as any);
        }

        // Refresh membership status
        const { data: membershipData } = await supabase
          .from("memberships")
          .select("id, user_id, gym_id, status, created_at, updated_at")
          .eq("id", membership.id)
          .single();

        if (membershipData) {
          setMembership(membershipData);
        }
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error("Error purchasing package:", error);
      toast.error(
        "Paket satın alınırken bir hata oluştu. Lütfen tekrar deneyin."
      );
    }
  };

  const handleSubmitReview = async () => {
    if (!user || !membership || membership.status !== "active" || !gym) {
      toast.error(
        "Değerlendirme yapabilmek için aktif bir üyeliğiniz olmalıdır."
      );
      return;
    }

    if (reviewRating === 0) {
      toast.error("Lütfen bir puan seçin.");
      return;
    }

    try {
      const reviewData = {
        user_id: user.id,
        gym_id: gym.id,
        rating: reviewRating,
        comment: reviewComment,
      };

      const result = await submitGymReview(reviewData);

      if (result.success) {
        toast.success("Başarılı", {
          description: result.message,
        });

        setReviewDialogOpen(false);

        // Refresh reviews
        const reviewsResponse = await getReviewsByGymId(gym.id);

        if (reviewsResponse.success && reviewsResponse.data) {
          setReviews(reviewsResponse.data.reviews);
          setAverageRating(reviewsResponse.data.averageRating);
        }

        // Set user review
        if (result.data) {
          setUserReview(result.data);
        }
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error("Error submitting review:", error);
    }
  };

  if (isLoading) {
    return <GymDetailSkeleton />;
  }

  if (!gym) {
    return (
      <div className="container py-8">
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <p className="text-xl font-bold">Salon bulunamadı</p>
            <p className="mt-2 text-muted-foreground">
              Aradığınız salon bulunamadı veya kaldırılmış olabilir.
            </p>
          </div>
        </div>
      </div>
    );
  }

  const canPurchasePackage =
    membership &&
    (membership.status === "approved_passive" ||
      membership.status === "active");
  const isActiveMember = membership && membership.status === "active";
  const isPendingMember =
    membership && membership.status === "pending_approval";
  const isRejectedMember = membership && membership.status === "rejected";
  const isGymOwner = user && gym.manager_user_id === user.id;

  return (
    <div className="container mx-auto py-8">
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        <div className="lg:col-span-2 space-y-6">
          <div>
            <h1 className="text-3xl font-bold">{gym.name}</h1>
            <div className="flex items-center gap-1 mt-2 text-muted-foreground">
              <MapPin className="h-4 w-4" />
              {gym.address}, {gym.district}, {gym.city}
            </div>
            <div className="flex items-center gap-4 mt-2">
              <div className="flex items-center gap-1">
                <Star className="h-4 w-4 fill-primary text-primary" />
                <span className="font-medium">{averageRating.toFixed(1)}</span>
                <span className="text-sm text-muted-foreground">
                  ({reviews.length} değerlendirme)
                </span>
              </div>
              {gym.gym_phone && (
                <div className="flex items-center gap-1 text-muted-foreground">
                  <Phone className="h-4 w-4" />
                  {gym.gym_phone}
                </div>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            {gym.cover_image_url ? (
              <div className="aspect-video overflow-hidden rounded-lg col-span-full">
                <img
                  src={gym.cover_image_url || "/placeholder.svg"}
                  alt={`${gym.name} - Kapak Fotoğrafı`}
                  className="h-full w-full object-cover"
                />
              </div>
            ) : (
              <div className="aspect-video overflow-hidden rounded-lg col-span-full bg-muted flex items-center justify-center">
                <p className="text-muted-foreground">Kapak fotoğrafı yok</p>
              </div>
            )}
          </div>

          <Tabs defaultValue="about">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="about">Hakkında</TabsTrigger>
              <TabsTrigger value="packages">Paketler</TabsTrigger>
              <TabsTrigger value="reviews">Değerlendirmeler</TabsTrigger>
              <TabsTrigger value="info">Bilgiler</TabsTrigger>
            </TabsList>

            <TabsContent value="about" className="space-y-4 pt-4">
              <div>
                <h3 className="text-lg font-medium">Salon Hakkında</h3>
                <p className="mt-2 text-muted-foreground">
                  {gym.description || "Bu salon için açıklama bulunmuyor."}
                </p>
              </div>

              <div>
                <h3 className="text-lg font-medium">Özellikler</h3>
                <div className="mt-2 flex flex-wrap gap-2">
                  {gym.features &&
                  Array.isArray(gym.features) &&
                  gym.features.length > 0 ? (
                    gym.features.map((feature: any) => (
                      <Badge key={feature as string} variant="secondary">
                        {feature as string}
                      </Badge>
                    ))
                  ) : (
                    <p className="text-muted-foreground">
                      Özellik bilgisi bulunmuyor.
                    </p>
                  )}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="packages" className="pt-4">
              {packages.length > 0 ? (
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  {packages.map((pkg) => (
                    <Card key={pkg.id}>
                      <CardHeader>
                        <CardTitle>{pkg.name}</CardTitle>
                        <CardDescription>
                          {(pkg.package_type === "monthly" ||
                            pkg.package_type === "quarterly" ||
                            pkg.package_type === "yearly" ||
                            pkg.package_type === "daily") &&
                          pkg.duration_days
                            ? `${pkg.duration_days} gün`
                            : pkg.package_type === "trial"
                            ? "Deneme paketi"
                            : ""}
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold">
                          {pkg.price_amount.toLocaleString()}{" "}
                          {pkg.currency === "TRY" ? "₺" : pkg.currency || "₺"}
                        </div>
                        <p className="mt-2 text-sm text-muted-foreground">
                          {pkg.description}
                        </p>
                      </CardContent>
                      <CardFooter>
                        {isGymOwner ? (
                          <Button className="w-full" disabled>
                            Kendi Salonunuz
                          </Button>
                        ) : canPurchasePackage ? (
                          <Button
                            className="w-full"
                            onClick={() => handlePurchasePackage(pkg.id)}
                          >
                            Satın Al
                          </Button>
                        ) : isPendingMember ? (
                          <Button className="w-full" disabled>
                            Üyelik Onayı Bekleniyor
                          </Button>
                        ) : isRejectedMember ? (
                          <Button className="w-full" disabled>
                            Üyelik İsteği Reddedildi
                          </Button>
                        ) : (
                          <Button
                            className="w-full"
                            disabled={!user || !requestSent}
                          >
                            {!user
                              ? "Giriş Yapın"
                              : !requestSent
                              ? "Önce Katılma İsteği Gönderin"
                              : "Yükleniyor..."}
                          </Button>
                        )}
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">
                    Bu salon için henüz paket bulunmuyor.
                  </p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="reviews" className="space-y-4 pt-4">
              {isActiveMember && !userReview && (
                <div className="mb-6">
                  <Dialog
                    open={reviewDialogOpen}
                    onOpenChange={setReviewDialogOpen}
                  >
                    <DialogTrigger asChild>
                      <Button>Değerlendirme Yap</Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Salon Değerlendirmesi</DialogTitle>
                        <DialogDescription>
                          {gym.name} salonunu değerlendirin. Deneyiminizi diğer
                          üyelerle paylaşın.
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4 py-4">
                        <div className="space-y-2">
                          <Label>Puanınız</Label>
                          <StarRating
                            rating={reviewRating}
                            onRatingChange={setReviewRating}
                            size={24}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="comment">Yorumunuz (Opsiyonel)</Label>
                          <Textarea
                            id="comment"
                            placeholder="Deneyiminizi paylaşın..."
                            value={reviewComment}
                            onChange={(e) => setReviewComment(e.target.value)}
                          />
                        </div>
                      </div>
                      <DialogFooter>
                        <Button onClick={handleSubmitReview}>Gönder</Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              )}

              {reviews.length > 0 ? (
                <>
                  {reviews.map((review) => (
                    <div key={review.id} className="rounded-lg border p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-3">
                          <Avatar>
                            <AvatarImage
                              src={
                                review.user.profile_picture_url ||
                                "/placeholder.svg"
                              }
                              alt={`${review.user.name || ""} ${
                                review.user.surname || ""
                              }`}
                            />
                            <AvatarFallback>
                              {review.user.name && review.user.surname
                                ? `${review.user.name.charAt(
                                    0
                                  )}${review.user.surname.charAt(0)}`
                                : "??"}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">
                              {review.user.name && review.user.surname
                                ? `${review.user.name} ${review.user.surname}`
                                : "İsimsiz Üye"}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {new Date(review.created_at).toLocaleDateString(
                                "tr-TR"
                              )}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-1">
                          <StarRating
                            rating={review.rating}
                            readonly
                            size={16}
                          />
                        </div>
                      </div>
                      {review.comment && (
                        <p className="mt-3 text-muted-foreground">
                          {review.comment}
                        </p>
                      )}
                    </div>
                  ))}
                  {reviews.length > 5 && (
                    <Button variant="outline" className="w-full">
                      Tüm Değerlendirmeleri Gör
                    </Button>
                  )}
                </>
              ) : (
                <div className="text-center py-8">
                  <p className="text-muted-foreground">
                    Bu salon için henüz değerlendirme bulunmuyor.
                  </p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="info" className="space-y-4 pt-4">
              <div>
                <h3 className="text-lg font-medium">İletişim</h3>
                <div className="mt-2 space-y-2">
                  {gym.gym_phone && (
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <Phone className="h-4 w-4" />
                      <span>{gym.gym_phone}</span>
                    </div>
                  )}
                  <div className="flex items-center gap-2 text-muted-foreground">
                    <MapPin className="h-4 w-4" />
                    <span>
                      {gym.address}, {gym.district}, {gym.city}
                    </span>
                  </div>
                </div>
              </div>

              <div className="h-[300px] rounded-lg border bg-muted flex items-center justify-center">
                <div className="text-center">
                  <MapPin className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                  <p className="text-sm text-muted-foreground">
                    Harita görünümü burada olacak
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    (Google Maps API entegrasyonu)
                  </p>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Salona Katıl</CardTitle>
              <CardDescription>
                Bu salona katılmak için istek gönderin
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Info className="h-4 w-4 text-muted-foreground" />
                  <p className="text-sm text-muted-foreground">
                    Salon yöneticisi isteğinizi onayladıktan sonra paket satın
                    alabilirsiniz.
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <MessageSquare className="h-4 w-4 text-muted-foreground" />
                  <p className="text-sm text-muted-foreground">
                    İsteğinizle birlikte profil bilgileriniz salon yöneticisiyle
                    paylaşılacaktır.
                  </p>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              {isGymOwner ? (
                <Button className="w-full" disabled>
                  Kendi Salonunuz
                </Button>
              ) : isPendingMember ? (
                <Button className="w-full" disabled>
                  İstek Gönderildi
                </Button>
              ) : isRejectedMember ? (
                <Button className="w-full" disabled>
                  İsteğiniz Reddedildi
                </Button>
              ) : isActiveMember || canPurchasePackage ? (
                <Button className="w-full" disabled>
                  Zaten Üyesiniz
                </Button>
              ) : (
                <Button
                  className="w-full"
                  onClick={handleJoinRequest}
                  disabled={!user || requestSent}
                >
                  {!user
                    ? "Giriş Yapın"
                    : requestSent
                    ? "İstek Gönderildi"
                    : "Katılma İsteği Gönder"}
                </Button>
              )}
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Duyurular</CardTitle>
            </CardHeader>
            <CardContent>
              {announcements.length > 0 ? (
                <div className="space-y-4">
                  {announcements.map((announcement) => (
                    <div
                      key={announcement.id}
                      className="rounded-lg border p-3"
                    >
                      <div className="font-medium">{announcement.title}</div>
                      <p className="mt-1 text-sm text-muted-foreground">
                        {announcement.content}
                      </p>
                      <div className="mt-2 text-xs text-muted-foreground">
                        {new Date(announcement.created_at).toLocaleDateString(
                          "tr-TR"
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-4">
                  <p className="text-muted-foreground">
                    Henüz duyuru bulunmuyor.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {isActiveMember && subscriptions.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Aktif Paketleriniz</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {subscriptions.map((subscription: any) => (
                    <div
                      key={subscription.id}
                      className="rounded-lg border p-3"
                    >
                      <div className="font-medium">
                        {subscription.gym_package.name}
                      </div>
                      <div className="mt-1 text-sm">
                        <div>
                          Başlangıç:{" "}
                          {new Date(subscription.start_date).toLocaleDateString(
                            "tr-TR"
                          )}
                        </div>
                        <div>
                          Bitiş:{" "}
                          {subscription.end_date
                            ? new Date(
                                subscription.end_date
                              ).toLocaleDateString("tr-TR")
                            : "Belirsiz"}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
