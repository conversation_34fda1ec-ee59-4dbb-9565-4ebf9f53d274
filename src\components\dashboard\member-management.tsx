"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Check,
  Search,
  X,
  Loader2,
  Plus,
  UserPlus,
  AlertTriangle,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from "lucide-react";

import { format } from "date-fns";
import { tr } from "date-fns/locale";
import { Memberships, Users } from "@/lib/supabase/types";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Di<PERSON>Footer,
  DialogDescription,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import {
  createMemberByManager,
  inviteMemberToGym,
  searchUsers,
} from "@/app/actions/user-actions";
import {
  getMembershipsByGymId,
  approveMembershipRequest as approveMembership,
  rejectMembershipRequest as rejectMembership,
  updateMembershipStatus,
} from "@/app/actions/membership-actions";

type MemberWithDetails = {
  membership: Memberships;
  user: Users;
};

// Üyelik durumları
const membershipStatuses = {
  all: { id: "all", label: "Tümü" },
  active: { id: "active", label: "Aktif" },
  approved_passive: { id: "approved_passive", label: "Pasif" },
  pending_approval: { id: "pending_approval", label: "Onay Bekleyen" },
};

// Yeni üye form şeması
const newUserSchema = z.object({
  email: z.string().email("Geçerli bir e-posta adresi giriniz"),
  name: z.string().min(2, "Ad en az 2 karakter olmalıdır"),
  surname: z.string().min(2, "Soyad en az 2 karakter olmalıdır"),
  gender: z.enum(["male", "female", "other", "prefer_not_to_say"], {
    required_error: "Lütfen bir cinsiyet seçin",
  }),
});

type NewUserFormValues = z.infer<typeof newUserSchema>;

interface MemberManagementProps {
  gymId: string;
}

export function MemberManagement({ gymId }: MemberManagementProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [membersByStatus, setMembersByStatus] = useState<
    Record<string, MemberWithDetails[]>
  >({
    all: [],
    active: [],
    approved_passive: [],
    pending_approval: [],
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [securityError, setSecurityError] = useState<string | null>(null);
  const [selectedTab, setSelectedTab] = useState("all");
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [addMemberTab, setAddMemberTab] = useState("search");

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalPages, setTotalPages] = useState(1);

  // Mevcut üye arama
  const [existingUserSearch, setExistingUserSearch] = useState("");
  const [searchResults, setSearchResults] = useState<Users[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [selectedMember, setSelectedMember] =
    useState<MemberWithDetails | null>(null);
  const [isDetailDialogOpen, setIsDetailDialogOpen] = useState(false);
  const [isStatusUpdating, setIsStatusUpdating] = useState(false);

  // React Hook Form için form tanımlama
  const newUserForm = useForm<NewUserFormValues>({
    resolver: zodResolver(newUserSchema),
    defaultValues: {
      email: "",
      name: "",
      surname: "",
      gender: "prefer_not_to_say",
    },
  });

  // Üyeleri ve istekleri al
  const fetchMembersAndRequests = async () => {
    setIsLoading(true);
    setError(null);

    if (!gymId) {
      setError("Salon ID'si bulunamadı");
      setIsLoading(false);
      return;
    }

    try {
      console.log("Fetching memberships for gymId:", gymId);

      // Server Action kullanarak üyelikleri getir
      const response = await getMembershipsByGymId(gymId);

      if (!response.success || !response.data) {
        throw new Error(response.error || "Üyelikler getirilemedi");
      }

      const membersData = response.data;
      console.log("Memberships data:", membersData);

      // Üyelikleri durumlarına göre ayır
      const allMembers = membersData || [];
      const activeMembers = allMembers.filter(
        (m: MemberWithDetails) => m.membership.status === "active"
      );
      const passiveMembers = allMembers.filter(
        (m: MemberWithDetails) => m.membership.status === "approved_passive"
      );
      const pendingMembers = allMembers.filter(
        (m: MemberWithDetails) => m.membership.status === "pending_approval"
      );

      setMembersByStatus({
        all: allMembers,
        active: activeMembers,
        approved_passive: passiveMembers,
        pending_approval: pendingMembers,
      });
    } catch (err) {
      console.error("Üyelikleri getirme hatası:", err);
      setError("Üyelikler yüklenirken bir hata oluştu. Lütfen tekrar deneyin.");
    } finally {
      setIsLoading(false);
    }
  };
  
  // Değişiklikleri takip et ve üyelikleri güncelle
  useEffect(() => {
    fetchMembersAndRequests();
  }, [gymId]);

  // Pagination için sayfa değişikliğini izle
  useEffect(() => {
    // Mevcut tab'deki toplam eleman sayısı
    const totalItems = membersByStatus[selectedTab]?.length || 0;
    
    // Toplam sayfa sayısını hesapla
    const calculatedTotalPages = Math.max(1, Math.ceil(totalItems / itemsPerPage));
    setTotalPages(calculatedTotalPages);

    // Geçerli sayfanın geçerliliğini kontrol et
    if (currentPage > calculatedTotalPages) {
      setCurrentPage(calculatedTotalPages);
    }
  }, [membersByStatus, selectedTab, itemsPerPage, currentPage]);

  // Paginate edilmiş ve filtrelenmiş üyeleri döndür
  const getPaginatedMembers = () => {
    const allMembers = membersByStatus[selectedTab] || [];
    
    // Önce arama filtresini uygula
    const filteredMembers = searchTerm 
      ? allMembers.filter(member => {
          const fullName = getUserFullName(member.user).toLowerCase();
          return fullName.includes(searchTerm.toLowerCase()) || 
                 member.user.email?.toLowerCase().includes(searchTerm.toLowerCase());
        })
      : allMembers;
    
    // Sayfa sayısını tekrar hesapla
    const calculatedTotalPages = Math.max(1, Math.ceil(filteredMembers.length / itemsPerPage));
    if (totalPages !== calculatedTotalPages) {
      setTotalPages(calculatedTotalPages);
    }
    
    // Pagination'a göre elemanları slice et
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = Math.min(startIndex + itemsPerPage, filteredMembers.length);
    
    return filteredMembers.slice(startIndex, endIndex);
  };

  // Sayfa değiştirme işleyicisi
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
    }
  };

  // Üyelik isteklerini onayla
  const handleApprove = async (id: string) => {
    try {
      setIsStatusUpdating(true);

      // Server Action kullanarak üyeliği onayla
      const response = await approveMembership(id);

      if (response.success) {
        fetchMembersAndRequests(); // Listeyi güncelle
      } else {
       
        console.error("Onay hatası:", response.error);
      }
    } catch (error) {
      console.error("Üyelik onaylama hatası:", error);
     
    } finally {
      setIsStatusUpdating(false);
    }
  };

  // Üyelik isteklerini reddet
  const handleReject = async (id: string) => {
    try {
      setIsStatusUpdating(true);

      // Server Action kullanarak üyeliği reddet
      const response = await rejectMembership(id);

      if (response.success) {
        fetchMembersAndRequests(); // Listeyi güncelle
      } else {
       
        console.error("Red hatası:", response.error);
      }
    } catch (error) {
      console.error("Üyelik reddetme hatası:", error);
    
    } finally {
      setIsStatusUpdating(false);
    }
  };

  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return "-";
    return format(new Date(dateString), "d MMMM yyyy", {
      locale: tr,
    });
  };

  const getUserInitials = (user: Users) => {
    if (!user?.name) return "??";
    const nameParts = `${user.name} ${user.surname || ""}`.trim().split(/\s+/);
    return nameParts
      .map((p) => p[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const getUserFullName = (user: Users) => {
    if (!user) return "";
    return `${user.name || ""} ${user.surname || ""}`.trim() || user.email;
  };

  // Mevcut kullanıcıları ara
  const searchExistingUsers = async () => {
    if (!existingUserSearch.trim()) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    try {
      // Mevcut üyeleri dışarıda bırakmak için
      if (!gymId) {
        toast.error("Lütfen bir salon seçin");
        return;
      }

      // Üye olarak zaten eklediğimiz kullanıcıları dışlamak için ID'leri alıyoruz
      const existingUserIds = membersByStatus.all.map(
        (m: MemberWithDetails) => m.user.id
      );

      // Server action ile kullanıcıları ara
      const response = await searchUsers(existingUserSearch, existingUserIds);

      if (!response.success) {
        throw new Error(response.error);
      }

      setSearchResults(response.data || []);
    } catch (error) {
      console.error("Kullanıcı arama hatası:", error);
      toast.error("Kullanıcı aranırken bir hata oluştu");
    } finally {
      setIsSearching(false);
    }
  };

  // Yeni kullanıcı oluştur
  const handleAddNewUser = async (values: NewUserFormValues) => {
    setIsSubmitting(true);

    if (!gymId) {
      toast.error("Bir salon seçin");
      setIsSubmitting(false);
      return;
    }

    try {
      // Server Action kullanarak yeni kullanıcı oluştur - obje olarak parametreleri gönderiyoruz
      const response = await createMemberByManager({
        email: values.email,
        name: values.name,
        surname: values.surname,
        gender: values.gender,
        gymId: gymId,
      });

      if (response.success) {
        toast.success("Kullanıcı başarıyla oluşturuldu ve davet edildi");
        fetchMembersAndRequests(); // Listeyi güncelle
        resetAddMemberDialog();
        setIsAddDialogOpen(false);
      } else {
        toast.error(response.error || "Kullanıcı oluşturulamadı");
      }
    } catch (error) {
      console.error("Kullanıcı oluşturma hatası:", error);
      toast.error("Kullanıcı oluşturulurken bir hata oluştu");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Mevcut kullanıcıyı davet et
  const inviteExistingUser = async (userId: string) => {
    if (!gymId) {
      toast.error("Bir salon seçin");
      return;
    }

    setIsSubmitting(true);
    try {
      // Server Action kullanarak kullanıcıyı davet et
      const response = await inviteMemberToGym(userId, gymId);

      if (response.success) {
        toast.success("Kullanıcı başarıyla davet edildi");
        fetchMembersAndRequests(); // Listeyi güncelle
        resetAddMemberDialog();
        setIsAddDialogOpen(false);
      } else {
        toast.error(response.error || "Kullanıcı davet edilemedi");
      }
    } catch (error) {
      console.error("Kullanıcı davet hatası:", error);
      toast.error("Kullanıcı davet edilirken bir hata oluştu");
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetAddMemberDialog = () => {
    setAddMemberTab("search");
    setExistingUserSearch("");
    setSearchResults([]);
    newUserForm.reset();
  };

  // Üyelik durumuna göre rozet göster
  const getMembershipBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge className="bg-green-500 hover:bg-green-600">Aktif</Badge>;
      case "approved_passive":
        return <Badge variant="secondary">Pasif</Badge>;
      case "pending_approval":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
            Onay Bekliyor
          </Badge>
        );
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Üyelik durumunu güncelle
  const handleStatusUpdate = async (id: string, newStatus: string) => {
    try {
      setIsStatusUpdating(true);

      let response;

      if (newStatus === "approved_passive") {
        // Onaylama action'ı
        response = await approveMembership(id);
      } else if (newStatus === "rejected") {
        // Reddetme action'ı
        response = await rejectMembership(id);
      } else {
        // Server action ile üyelik durumunu güncelle
        response = await updateMembershipStatus(id, newStatus);
      }

      if (response.success) {
        toast.success("Üyelik durumu güncellendi");

        if (selectedMember && selectedMember.membership.id === id) {
          // Seçilen üyeliğin durumunu güncelle
          setSelectedMember({
            ...selectedMember,
            membership: {
              ...selectedMember.membership,
              status: newStatus,
            },
          });
        }

        fetchMembersAndRequests(); // Listeyi güncelle
      } else {
        toast.error("Durum güncellenirken bir hata oluştu");
        console.error("Güncelleme hatası:", response.error);
      }
    } catch (error) {
      console.error("Durum güncelleme hatası:", error);
      toast.error("Bir hata oluştu");
    } finally {
      setIsStatusUpdating(false);
    }
  };

  // Üye detaylarını göster
  const showMemberDetails = async (member: MemberWithDetails) => {
    setSelectedMember(member);
    setIsDetailDialogOpen(true);
  };

  if (securityError) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <AlertTriangle className="h-8 w-8 text-destructive mb-4" />
        <p className="text-destructive font-medium mb-2">{securityError}</p>
        <p className="text-muted-foreground mb-4">
          Bu sayfaya erişim yetkiniz bulunmuyor.
        </p>
        <Button onClick={fetchMembersAndRequests}>Tekrar Dene</Button>
      </div>
    );
  }

  if (isLoading && !membersByStatus.all.length) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <Loader2 className="h-8 w-8 text-primary animate-spin mb-4" />
        <p className="text-muted-foreground">Üyeler yükleniyor...</p>
      </div>
    );
  }

  if (error && !membersByStatus.all.length) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <p className="text-destructive mb-4">{error}</p>
        <Button onClick={fetchMembersAndRequests}>Yeniden Dene</Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Üye Yönetimi</h2>
          <p className="text-muted-foreground">
            Salonunuzun üyelerini ve üyelik isteklerini yönetin.
          </p>
        </div>

        <Dialog
          open={isAddDialogOpen}
          onOpenChange={(open) => {
            setIsAddDialogOpen(open);
            if (!open) resetAddMemberDialog();
          }}
        >
          <DialogTrigger asChild>
            <Button className="ml-auto">
              <UserPlus className="mr-2 h-4 w-4" />
              Üye Ekle
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Üye Ekle</DialogTitle>
              <DialogDescription>
                Yeni üye ekleyin veya mevcut üyeleri arayarak davet edin
              </DialogDescription>
            </DialogHeader>

            <Tabs
              defaultValue="search"
              value={addMemberTab}
              onValueChange={setAddMemberTab}
              className="pt-2"
            >
              <TabsList className="grid grid-cols-2">
                <TabsTrigger value="search">Mevcut Kullanıcı Ara</TabsTrigger>
                <TabsTrigger value="create">Yeni Kullanıcı Oluştur</TabsTrigger>
              </TabsList>

              <TabsContent value="search" className="space-y-4 py-4">
                <div className="space-y-2">
                  <div className="flex gap-2">
                    <Input
                      placeholder="İsim, soyisim veya e-posta ara..."
                      value={existingUserSearch}
                      onChange={(e) => setExistingUserSearch(e.target.value)}
                      className="flex-1"
                    />
                    <Button
                      onClick={searchExistingUsers}
                      disabled={isSearching || existingUserSearch.length < 3}
                    >
                      {isSearching ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Search className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    En az 3 karakter girerek arama yapabilirsiniz
                  </p>
                </div>

                <div className="space-y-2">
                  {searchResults.length === 0 &&
                    existingUserSearch.length >= 3 &&
                    !isSearching && (
                      <p className="text-sm text-center text-muted-foreground py-4">
                        Arama kriterlerinize uygun kullanıcı bulunamadı
                      </p>
                    )}

                  {searchResults.map((user) => (
                    <div
                      key={user.id}
                      className="flex items-center justify-between p-3 border rounded-md"
                    >
                      <div className="flex items-center gap-3">
                        <Avatar>
                          <AvatarImage
                            src={user.profile_picture_url || ""}
                            alt={user.name || ""}
                          />
                          <AvatarFallback>
                            {getUserInitials(user)}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">
                            {getUserFullName(user)}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {user.email}
                          </div>
                        </div>
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => inviteExistingUser(user.id)}
                        disabled={isSubmitting}
                      >
                        {isSubmitting ? (
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        ) : (
                          <UserPlus className="h-4 w-4 mr-2" />
                        )}
                        Davet Et
                      </Button>
                    </div>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="create" className="py-4">
                <Form {...newUserForm}>
                  <form
                    onSubmit={newUserForm.handleSubmit(handleAddNewUser)}
                    className="space-y-4"
                  >
                    <FormField
                      control={newUserForm.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>E-posta</FormLabel>
                          <FormControl>
                            <Input placeholder="<EMAIL>" {...field} />
                          </FormControl>
                          <FormDescription>
                            Üyenin e-posta adresi
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={newUserForm.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Ad</FormLabel>
                            <FormControl>
                              <Input placeholder="Ad" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={newUserForm.control}
                        name="surname"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Soyad</FormLabel>
                            <FormControl>
                              <Input placeholder="Soyad" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={newUserForm.control}
                      name="gender"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Cinsiyet</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Cinsiyet seçin" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="male">Erkek</SelectItem>
                              <SelectItem value="female">Kadın</SelectItem>
                              <SelectItem value="other">Diğer</SelectItem>
                              <SelectItem value="prefer_not_to_say">
                                Belirtmek İstemiyorum
                              </SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <DialogFooter className="pt-4">
                      <Button type="submit" disabled={isSubmitting}>
                        {isSubmitting ? (
                          <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        ) : (
                          <Plus className="h-4 w-4 mr-2" />
                        )}
                        Üye Oluştur
                      </Button>
                    </DialogFooter>
                  </form>
                </Form>
              </TabsContent>
            </Tabs>
          </DialogContent>
        </Dialog>
      </div>

      <Tabs
        defaultValue="all"
        value={selectedTab}
        onValueChange={setSelectedTab}
      >
        <TabsList className="flex flex-wrap">
          <TabsTrigger value="all">
            Tümü
            {membersByStatus.all.length > 0 && (
              <Badge variant="outline" className="ml-2">
                {membersByStatus.all.length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="active">
            Aktif Üyeler
            {membersByStatus.active.length > 0 && (
              <Badge variant="outline" className="ml-2">
                {membersByStatus.active.length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="approved_passive">
            Pasif Üyeler
            {membersByStatus.approved_passive.length > 0 && (
              <Badge variant="outline" className="ml-2">
                {membersByStatus.approved_passive.length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="pending_approval">
            Onay Bekleyenler
            {membersByStatus.pending_approval.length > 0 && (
              <Badge variant="outline" className="ml-2">
                {membersByStatus.pending_approval.length}
              </Badge>
            )}
          </TabsTrigger>
        </TabsList>

        <div className="mt-4 flex items-center gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Üye ara..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Button variant="outline">Filtrele</Button>
        </div>

        {/* Yükleme hata mesajı */}
        {error && (
          <div className="my-4 p-4 border border-destructive/50 rounded-md bg-destructive/10">
            <p className="text-destructive font-medium">{error}</p>
            <Button
              onClick={fetchMembersAndRequests}
              variant="outline"
              className="mt-2"
            >
              <Loader2
                className={`mr-2 h-4 w-4 ${isLoading ? "animate-spin" : ""}`}
              />
              Üyeleri Yeniden Yükle
            </Button>
          </div>
        )}

        {/* Yeniden yükleme düğmesi */}
        <div className="flex justify-end mt-4">
          <Button
            onClick={fetchMembersAndRequests}
            variant="outline"
            disabled={isLoading}
          >
            <Loader2
              className={`mr-2 h-4 w-4 ${isLoading ? "animate-spin" : ""}`}
            />
            Üyeleri Yeniden Yükle
          </Button>
        </div>

        {Object.keys(membershipStatuses).map((statusKey) => (
          <TabsContent
            key={statusKey}
            value={statusKey}
            className="space-y-4 mt-2"
          >
            <Card>
              <CardHeader>
                <CardTitle>
                  {statusKey === "all"
                    ? "Tüm Üyeler"
                    : statusKey === "active"
                    ? "Aktif Üyeler"
                    : statusKey === "approved_passive"
                    ? "Pasif Üyeler"
                    : "Onay Bekleyen Üyeler"}
                </CardTitle>
                <CardDescription>
                  {membersByStatus[statusKey].length} üye
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Üye</TableHead>
                      <TableHead>Durum</TableHead>
                      <TableHead>Kayıt Tarihi</TableHead>
                      <TableHead className="text-right">İşlemler</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {isLoading ? (
                      <TableRow>
                        <TableCell colSpan={4} className="h-24 text-center">
                          <div className="flex justify-center">
                            <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : getPaginatedMembers().length > 0 ? (
                      getPaginatedMembers().map((member) => (
                        <TableRow key={member.membership.id}>
                          <TableCell>
                            <div className="flex items-center gap-3">
                              <Avatar>
                                <AvatarImage
                                  src={member.user?.profile_picture_url || ""}
                                  alt={getUserFullName(member.user)}
                                />
                                <AvatarFallback>
                                  {getUserInitials(member.user)}
                                </AvatarFallback>
                              </Avatar>
                              <div>
                                <div className="font-medium">
                                  {getUserFullName(member.user)}
                                </div>
                                <div className="text-sm text-muted-foreground">
                                  {member.user?.email}
                                </div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            {getMembershipBadge(member.membership.status)}
                          </TableCell>
                          <TableCell>
                            {formatDate(member.membership.created_at)}
                          </TableCell>
                          <TableCell className="text-right">
                            {member.membership.status === "pending_approval" ? (
                              <div className="flex justify-end gap-2">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() =>
                                    handleApprove(member.membership.id)
                                  }
                                  disabled={isStatusUpdating}
                                >
                                  <Check className="mr-2 h-4 w-4" />
                                  Onayla
                                </Button>
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() =>
                                    handleReject(member.membership.id)
                                  }
                                  disabled={isStatusUpdating}
                                >
                                  <X className="mr-2 h-4 w-4" />
                                  Reddet
                                </Button>
                              </div>
                            ) : (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => showMemberDetails(member)}
                              >
                                Detaylar
                              </Button>
                            )}
                          </TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={4} className="h-24 text-center">
                          {searchTerm ? 
                            "Arama kriterine uygun üye bulunamadı." :
                            "Bu kategoride üye bulunamadı."}
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
                
                {!isLoading && getPaginatedMembers().length > 0 && (
                  <div className="flex items-center justify-end space-x-2 py-4">
                    <div className="flex-1 text-sm text-muted-foreground">
                      Toplam {membersByStatus[statusKey]?.length || 0} üyeden{" "}
                      {(currentPage - 1) * itemsPerPage + 1}-
                      {Math.min(
                        currentPage * itemsPerPage,
                        membersByStatus[statusKey]?.length || 0
                      )}{" "}
                      arası gösteriliyor
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(1)}
                        disabled={currentPage === 1}
                      >
                        <ChevronsLeft className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                      >
                        <ChevronLeft className="h-4 w-4" />
                      </Button>
                      <div className="text-sm font-medium">
                        Sayfa {currentPage}/{totalPages}
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                      >
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handlePageChange(totalPages)}
                        disabled={currentPage === totalPages}
                      >
                        <ChevronsRight className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        ))}
      </Tabs>

      {/* Üye Detay Dialog'u */}
      <Dialog open={isDetailDialogOpen} onOpenChange={setIsDetailDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Üye Detayları</DialogTitle>
            <DialogDescription>
              Üye bilgilerini görüntüleyin ve durumunu değiştirin
            </DialogDescription>
          </DialogHeader>

          {selectedMember && (
            <div className="space-y-6">
              <div className="flex items-center gap-4">
                <Avatar className="h-16 w-16">
                  <AvatarImage
                    src={selectedMember.user?.profile_picture_url || ""}
                    alt={getUserFullName(selectedMember.user)}
                  />
                  <AvatarFallback className="text-lg">
                    {getUserInitials(selectedMember.user)}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="font-semibold text-lg">
                    {getUserFullName(selectedMember.user)}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {selectedMember.user.email}
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-muted-foreground">Üyelik Durumu</p>
                  <p>{getMembershipBadge(selectedMember.membership.status)}</p>
                </div>
                <div>
                  <p className="text-sm text-muted-foreground">Kayıt Tarihi</p>
                  <p>{formatDate(selectedMember.membership.created_at)}</p>
                </div>
                {selectedMember.user.gender && (
                  <div>
                    <p className="text-sm text-muted-foreground">Cinsiyet</p>
                    <p>
                      {selectedMember.user.gender === "male"
                        ? "Erkek"
                        : selectedMember.user.gender === "female"
                        ? "Kadın"
                        : selectedMember.user.gender === "other"
                        ? "Diğer"
                        : "Belirtilmemiş"}
                    </p>
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <p className="text-sm font-medium">Üyelik Durumu Değiştir</p>
                <Select
                  defaultValue={selectedMember.membership.status}
                  disabled={isStatusUpdating}
                  onValueChange={(value) =>
                    handleStatusUpdate(selectedMember.membership.id, value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Aktif</SelectItem>
                    <SelectItem value="approved_passive">Pasif</SelectItem>
                    <SelectItem value="pending_approval">
                      Onay Bekleyen
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDetailDialogOpen(false)}
            >
              Kapat
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
