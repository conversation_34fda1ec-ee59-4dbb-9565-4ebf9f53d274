"use client";

import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Loader2, Plus, Package, DollarSign, Edit, Trash2 } from "lucide-react";
import { getGymBySlug } from "@/app/actions/gym-actions";
import {
  getPackagesWithPurchaseCountsByGymId,
  deleteGymPackage,
} from "@/app/actions/package-actions";
import { Tables } from "@/lib/supabase/types";
import { GymPackageForm } from "@/components/gym-package-form";
import { useToast } from "@/components/ui/use-toast";

type PackageWithCount = Tables<"gym_packages"> & {
  purchase_count: number;
};

export default function PackagesPage() {
  const params = useParams();
  const slug = params.slug as string;
  const [gym, setGym] = useState<Tables<"gyms"> | null>(null);
  const [packages, setPackages] = useState<PackageWithCount[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingPackage, setEditingPackage] =
    useState<Tables<"gym_packages"> | null>(null);
  const [deletingPackage, setDeletingPackage] =
    useState<Tables<"gym_packages"> | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const { toast } = useToast();

  const fetchData = async () => {
    if (!slug) return;

    try {
      setLoading(true);

      // Salon bilgilerini al
      const gymResponse = await getGymBySlug(slug);
      if (gymResponse.success && gymResponse.data) {
        setGym(gymResponse.data);

        // Paketleri al
        const packagesResponse = await getPackagesWithPurchaseCountsByGymId(
          gymResponse.data.id
        );
        if (packagesResponse.success && packagesResponse.data) {
          setPackages(packagesResponse.data);
        }
      }
    } catch (error) {
      console.error("Veriler yüklenirken hata:", error);
      toast({
        title: "Hata",
        description: "Veriler yüklenirken bir hata oluştu.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [slug]);

  const handleDeletePackage = async () => {
    if (!deletingPackage) return;

    setDeleteLoading(true);
    try {
      const response = await deleteGymPackage(deletingPackage.id);

      if (response.success) {
        toast({
          title: "Başarılı",
          description: "Paket başarıyla silindi.",
        });
        setDeletingPackage(null);
        fetchData(); // Listeyi yenile
      } else {
        toast({
          title: "Hata",
          description: response.error || "Paket silinirken bir hata oluştu.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Paket silme hatası:", error);
      toast({
        title: "Hata",
        description: "Beklenmeyen bir hata oluştu.",
        variant: "destructive",
      });
    } finally {
      setDeleteLoading(false);
    }
  };

  // İstatistikleri hesapla
  const totalPackages = packages.length;
  const activePackages = packages.filter((pkg) => pkg.is_active).length;
  const averagePrice =
    packages.length > 0
      ? packages.reduce((sum, pkg) => sum + pkg.price_amount, 0) /
        packages.length
      : 0;

  const getPackageTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      monthly: "Aylık",
      quarterly: "3 Aylık",
      yearly: "Yıllık",
      session: "Giriş Bazlı",
      daily: "Günlük",
      trial: "Deneme",
    };
    return labels[type] || type;
  };

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (!gym) {
    return (
      <div className="text-center py-8">
        <h2 className="text-xl font-semibold">Salon bulunamadı</h2>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Paketler</h1>
          <p className="text-muted-foreground">
            {gym.name} salonunuzun üyelik paketleri
          </p>
        </div>
        <Button onClick={() => setShowAddForm(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Yeni Paket Ekle
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Toplam Paket</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalPackages}</div>
            <p className="text-xs text-muted-foreground">
              Oluşturulan paket sayısı
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Aktif Paket</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activePackages}</div>
            <p className="text-xs text-muted-foreground">Satışa açık paket</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">
              Ortalama Fiyat
            </CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₺{averagePrice.toFixed(2)}</div>
            <p className="text-xs text-muted-foreground">
              Paket ortalama fiyatı
            </p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Paket Listesi</CardTitle>
        </CardHeader>
        <CardContent>
          {packages.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium">Henüz paket bulunmuyor</p>
              <p className="text-sm">
                İlk paketinizi oluşturmak için "Yeni Paket Ekle" butonunu
                kullanın.
              </p>
              <Button
                className="mt-4"
                variant="outline"
                onClick={() => setShowAddForm(true)}
              >
                <Plus className="h-4 w-4 mr-2" />
                İlk Paketimi Oluştur
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {packages.map((pkg) => (
                <div
                  key={pkg.id}
                  className="flex items-center justify-between p-4 border rounded-lg"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <h3 className="font-semibold">{pkg.name}</h3>
                      <Badge variant={pkg.is_active ? "default" : "secondary"}>
                        {pkg.is_active ? "Aktif" : "Pasif"}
                      </Badge>
                      <Badge variant="outline">
                        {getPackageTypeLabel(pkg.package_type)}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      {pkg.description}
                    </p>
                    <div className="flex items-center gap-4 mt-2 text-sm">
                      <span className="font-medium">₺{pkg.price_amount}</span>
                      {pkg.duration_days && (
                        <span>{pkg.duration_days} gün</span>
                      )}
                      <span className="text-muted-foreground">
                        {pkg.purchase_count} satış
                      </span>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setEditingPackage(pkg)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setDeletingPackage(pkg)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Paket Ekleme/Düzenleme Formu */}
      {gym && (
        <GymPackageForm
          open={showAddForm || !!editingPackage}
          onOpenChange={(open) => {
            if (!open) {
              setShowAddForm(false);
              setEditingPackage(null);
            }
          }}
          gymId={gym.id}
          onSuccess={() => {
            fetchData();
            setEditingPackage(null);
          }}
          editPackage={editingPackage}
        />
      )}

      {/* Silme Onay Dialog'u */}
      <AlertDialog
        open={!!deletingPackage}
        onOpenChange={() => setDeletingPackage(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Paketi Sil</AlertDialogTitle>
            <AlertDialogDescription>
              "{deletingPackage?.name}" paketini silmek istediğinizden emin
              misiniz? Bu işlem geri alınamaz ve paket ile ilgili tüm veriler
              silinecektir.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={deleteLoading}>
              İptal
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeletePackage}
              disabled={deleteLoading}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {deleteLoading && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              Sil
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
